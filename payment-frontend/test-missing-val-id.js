#!/usr/bin/env node

/**
 * Test script to verify SSL Commerce success callback handling
 * when val_id parameter is missing
 */

const API_BASE_URL = 'http://127.0.0.1:8000/api';

async function testSuccessCallback(method, params, description) {
  try {
    console.log(`\n🧪 Testing: ${description}`);
    console.log(`Method: ${method}`);
    console.log(`Params:`, JSON.stringify(params, null, 2));

    const url = `${API_BASE_URL}/payment/success`;
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'SSL-Commerce-Browser/1.0'
      },
    };

    if (method === 'GET') {
      const queryString = new URLSearchParams(params).toString();
      const fullUrl = `${url}?${queryString}`;
      console.log(`URL: ${fullUrl}`);
      const response = await fetch(fullUrl, options);
      const result = await response.text();
      
      console.log(`Status: ${response.status}`);
      console.log(`Response: ${result}`);
      
      return { status: response.status, data: result };
    } else {
      options.body = JSON.stringify(params);
      console.log(`URL: ${url}`);
      const response = await fetch(url, options);
      const result = await response.text();
      
      console.log(`Status: ${response.status}`);
      console.log(`Response: ${result}`);
      
      return { status: response.status, data: result };
    }
  } catch (error) {
    console.error(`❌ Error:`, error.message);
    return { error: error.message };
  }
}

async function runMissingValIdTests() {
  console.log('🚀 Testing SSL Commerce Success Callback - Missing val_id Scenarios...');
  
  // Test 1: Specific transaction ID mentioned in the issue
  await testSuccessCallback('POST', {
    tran_id: 'TRX_20250618145506_KGUCAA',
    status: 'SUCCESS',
    amount: '100.00',
    bank_tran_id: 'BANK123456789'
  }, 'Specific transaction ID from issue (status-based validation)');

  // Test 2: Missing val_id but with valid status (GET)
  await testSuccessCallback('GET', {
    tran_id: 'TRX_TEST_NO_VALID_001',
    status: 'VALID',
    amount: '150.00',
    bank_tran_id: 'BANK987654321',
    card_type: 'VISA'
  }, 'Missing val_id with VALID status (GET request)');

  // Test 3: Missing val_id but with SUCCESS status (POST)
  await testSuccessCallback('POST', {
    tran_id: 'TRX_TEST_NO_VALID_002',
    status: 'SUCCESS',
    amount: '200.00',
    bank_tran_id: 'BANK111222333'
  }, 'Missing val_id with SUCCESS status (POST request)');

  // Test 4: Missing val_id and status, but with amount and bank_tran_id
  await testSuccessCallback('POST', {
    tran_id: 'TRX_TEST_MINIMAL_001',
    amount: '75.50',
    bank_tran_id: 'BANK444555666',
    card_type: 'MASTERCARD'
  }, 'Minimal validation - only amount and bank_tran_id');

  // Test 5: Missing val_id, status, and amount (should fail)
  await testSuccessCallback('GET', {
    tran_id: 'TRX_TEST_INSUFFICIENT_001'
  }, 'Insufficient data - only transaction ID');

  // Test 6: Valid callback with val_id (for comparison)
  await testSuccessCallback('POST', {
    tran_id: 'TRX_TEST_FULL_001',
    val_id: 'VAL123456789',
    status: 'VALID',
    amount: '300.00',
    bank_tran_id: 'BANK777888999'
  }, 'Full validation with val_id (for comparison)');

  // Test 7: Amount mismatch scenario
  await testSuccessCallback('POST', {
    tran_id: 'TRX_TEST_AMOUNT_MISMATCH',
    status: 'SUCCESS',
    amount: '999999.99',  // Deliberately wrong amount
    bank_tran_id: 'BANK000111222'
  }, 'Amount mismatch detection');

  // Test 8: Different success status variations
  await testSuccessCallback('GET', {
    tran_id: 'TRX_TEST_VALIDATED_001',
    status: 'VALIDATED',
    amount: '125.75'
  }, 'Alternative success status - VALIDATED');

  await testSuccessCallback('POST', {
    tran_id: 'TRX_TEST_SUCCESSFUL_001',
    status: 'SUCCESSFUL',
    amount: '89.99',
    card_type: 'AMEX'
  }, 'Alternative success status - SUCCESSFUL');

  console.log('\n✅ Missing val_id tests completed!');
  console.log('\n📝 Expected Results:');
  console.log('- Transactions with valid status should use status-based validation');
  console.log('- Transactions with only amount/bank_tran_id should use minimal validation');
  console.log('- Transactions with insufficient data should fail gracefully');
  console.log('- Amount mismatches should be detected and rejected');
  console.log('- All validation methods should be logged for debugging');
  console.log('- Payment records not found should return descriptive error messages');
}

// Run tests
runMissingValIdTests().catch(console.error);
