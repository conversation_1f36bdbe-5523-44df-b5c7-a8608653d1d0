#!/usr/bin/env node

/**
 * Test script to simulate SSL Commerce success callbacks
 * This helps verify the improved success method works correctly
 */

const API_BASE_URL = 'http://127.0.0.1:8000/api';

async function testSuccessCallback(method, params, description) {
  try {
    console.log(`\n🧪 Testing: ${description}`);
    console.log(`Method: ${method}`);
    console.log(`Params:`, params);

    const url = `${API_BASE_URL}/payment/success`;
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'SSL-Commerce-Test/1.0'
      },
    };

    if (method === 'GET') {
      const queryString = new URLSearchParams(params).toString();
      const fullUrl = `${url}?${queryString}`;
      console.log(`URL: ${fullUrl}`);
      const response = await fetch(fullUrl, options);
      const result = await response.text();
      
      console.log(`Status: ${response.status}`);
      console.log(`Response: ${result}`);
      
      return { status: response.status, data: result };
    } else {
      options.body = JSON.stringify(params);
      console.log(`URL: ${url}`);
      const response = await fetch(url, options);
      const result = await response.text();
      
      console.log(`Status: ${response.status}`);
      console.log(`Response: ${result}`);
      
      return { status: response.status, data: result };
    }
  } catch (error) {
    console.error(`❌ Error:`, error.message);
    return { error: error.message };
  }
}

async function runSuccessTests() {
  console.log('🚀 Testing SSL Commerce Success Callback Improvements...');
  
  // Test 1: Missing transaction ID
  await testSuccessCallback('GET', {
    val_id: 'test_val_123',
    amount: '100.00',
    status: 'VALID'
  }, 'Missing transaction ID (should fail gracefully)');

  // Test 2: Valid success callback (GET)
  await testSuccessCallback('GET', {
    tran_id: 'TRX_20250618_TEST001',
    val_id: 'test_val_123',
    amount: '100.00',
    status: 'VALID'
  }, 'Valid success callback via GET');

  // Test 3: Valid success callback (POST)
  await testSuccessCallback('POST', {
    tran_id: 'TRX_20250618_TEST002',
    val_id: 'test_val_456',
    amount: '200.00',
    status: 'VALID'
  }, 'Valid success callback via POST');

  // Test 4: Success without val_id (some SSL Commerce configs)
  await testSuccessCallback('GET', {
    tran_id: 'TRX_20250618_TEST003',
    amount: '150.00',
    status: 'SUCCESS'
  }, 'Success without val_id');

  // Test 5: Invalid status
  await testSuccessCallback('POST', {
    tran_id: 'TRX_20250618_TEST004',
    val_id: 'test_val_789',
    amount: '300.00',
    status: 'FAILED'
  }, 'Invalid status (should redirect to fail)');

  // Test 6: Payment not found (expected for test data)
  await testSuccessCallback('GET', {
    tran_id: 'NONEXISTENT_TRX',
    val_id: 'test_val_999',
    amount: '50.00',
    status: 'VALID'
  }, 'Payment not found (expected for test data)');

  console.log('\n✅ Success callback tests completed!');
  console.log('\n📝 Expected Results:');
  console.log('- Missing tran_id: Should return error with proper message');
  console.log('- Valid callbacks: Should attempt validation (may fail due to test data)');
  console.log('- Invalid status: Should redirect to fail page');
  console.log('- Payment not found: Should return 404 with proper error message');
  console.log('- All responses should be properly formatted and logged');
}

// Run tests
runSuccessTests().catch(console.error);
