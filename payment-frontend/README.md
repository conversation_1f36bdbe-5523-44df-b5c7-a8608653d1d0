# BacBon Payment Frontend

A modern React-based payment interface for SSL Commerce integration with the BacBon tutoring platform.

## Features

- 📦 **Package Selection**: Browse and select from available subscription packages
- 💳 **Payment Form**: Secure payment form with validation
- 🔄 **Payment Status**: Handle success, failure, and cancellation scenarios
- 📱 **Responsive Design**: Mobile-friendly interface using Tailwind CSS
- 🔗 **API Integration**: Seamless integration with Laravel backend

## Tech Stack

- **React 18** - Frontend framework
- **Vite** - Build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework
- **React Router** - Client-side routing
- **Axios** - HTTP client for API calls
- **Lucide React** - Beautiful icons

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Running Laravel backend at `http://127.0.0.1:8000`

### Installation

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

3. Open your browser and navigate to `http://localhost:5173`

## API Integration

The frontend integrates with the following Laravel API endpoints:

### Package & Category Endpoints
- `GET /api/open/elsa-categories` - Fetch available categories
- `GET /api/open/elsa-packages/{category_id}` - Fetch packages by category

### Payment Endpoints
- `POST /api/payment/pay` - Initiate payment
- `GET /api/payment/success` - Handle successful payment
- `GET /api/payment/fail` - Handle failed payment
- `GET /api/payment/cancel` - Handle cancelled payment

## Payment Flow

1. **Package Selection**: User selects a category and package
2. **Payment Form**: User fills in billing information and payment details
3. **SSL Commerce**: User is redirected to SSL Commerce payment gateway
4. **Payment Status**: User is redirected back with payment status

## Environment Configuration

The API base URL is configured in `src/services/api.js`:

```javascript
const API_BASE_URL = 'http://127.0.0.1:8000/api';
```

Update this URL to match your Laravel backend URL.

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## SSL Commerce Integration

The payment system integrates with SSL Commerce payment gateway:

1. Payment is initiated via the Laravel backend
2. User is redirected to SSL Commerce
3. After payment, user is redirected back to the frontend
4. Payment status is verified with the backend
