#!/usr/bin/env node

/**
 * Simple test script to verify payment API endpoints
 * Run with: node test-payment.js
 */

const API_BASE_URL = 'http://127.0.0.1:8000/api';

async function testEndpoint(method, endpoint, data = null) {
  try {
    const url = `${API_BASE_URL}${endpoint}`;
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    console.log(`\n🧪 Testing ${method} ${endpoint}`);
    const response = await fetch(url, options);
    const result = await response.text();
    
    console.log(`Status: ${response.status}`);
    console.log(`Response: ${result}`);
    
    return { status: response.status, data: result };
  } catch (error) {
    console.error(`❌ Error testing ${endpoint}:`, error.message);
    return { error: error.message };
  }
}

async function runTests() {
  console.log('🚀 Starting Payment API Tests...');
  
  // Test 1: Get Categories
  await testEndpoint('GET', '/open/elsa-categories');
  
  // Test 2: Get Packages (assuming category ID 1 exists)
  await testEndpoint('GET', '/open/elsa-packages/1');
  
  // Test 3: Test Payment Success (GET)
  await testEndpoint('GET', '/payment/success?tran_id=test123&val_id=test456');
  
  // Test 4: Test Payment Success (POST)
  await testEndpoint('POST', '/payment/success', {
    tran_id: 'test123',
    val_id: 'test456'
  });
  
  // Test 5: Test Payment Fail (GET)
  await testEndpoint('GET', '/payment/fail?tran_id=test123');
  
  // Test 6: Test Payment Fail (POST)
  await testEndpoint('POST', '/payment/fail', {
    tran_id: 'test123',
    status: 'FAILED'
  });
  
  // Test 7: Test Payment Cancel (GET)
  await testEndpoint('GET', '/payment/cancel?tran_id=test123');
  
  // Test 8: Test Payment Cancel (POST)
  await testEndpoint('POST', '/payment/cancel', {
    tran_id: 'test123',
    status: 'CANCELLED'
  });
  
  console.log('\n✅ All tests completed!');
  console.log('\n📝 Notes:');
  console.log('- "Payment not found" responses are expected for test transaction IDs');
  console.log('- Status 200 means the endpoint is working correctly');
  console.log('- Status 404 means payment record not found (expected for test data)');
}

// Run tests
runTests().catch(console.error);
