#!/usr/bin/env node

/**
 * Comprehensive SSL Commerce Debug Script
 * This script helps identify why successful SSL Commerce payments are being marked as failed
 */

const API_BASE_URL = 'http://127.0.0.1:8000/api';

async function makeRequest(method, endpoint, data = null, description = '') {
  try {
    console.log(`\n🔍 ${description}`);
    console.log(`${method} ${API_BASE_URL}${endpoint}`);
    
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'SSL-Commerce-Debug/1.0'
      },
    };

    let url = `${API_BASE_URL}${endpoint}`;
    
    if (method === 'GET' && data) {
      const queryString = new URLSearchParams(data).toString();
      url += `?${queryString}`;
      console.log(`Query: ${queryString}`);
    } else if (data) {
      options.body = JSON.stringify(data);
      console.log(`Body: ${JSON.stringify(data, null, 2)}`);
    }

    const response = await fetch(url, options);
    const result = await response.text();
    
    console.log(`Status: ${response.status}`);
    console.log(`Response: ${result}`);
    
    return { status: response.status, data: result, success: response.ok };
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    return { error: error.message };
  }
}

async function debugSSLCommerceIssue() {
  console.log('🚀 SSL Commerce Payment Debug Session');
  console.log('=====================================');
  
  // Step 1: Test the debug endpoint
  console.log('\n📋 STEP 1: Testing Debug Endpoint');
  await makeRequest('POST', '/payment/debug', {
    tran_id: 'TRX_20250618145506_KGUCAA',
    status: 'SUCCESS',
    amount: '499.99'
  }, 'Debug endpoint with sample SSL Commerce data');

  // Step 2: Check payment status
  console.log('\n📋 STEP 2: Checking Payment Status');
  await makeRequest('GET', '/payment/status/TRX_20250618145506_KGUCAA', null, 'Get current payment status');

  // Step 3: Test various SSL Commerce callback formats
  console.log('\n📋 STEP 3: Testing Different SSL Commerce Callback Formats');
  
  // Format 1: Standard SSL Commerce success callback
  await makeRequest('POST', '/payment/success', {
    tran_id: 'TRX_20250618145506_KGUCAA',
    val_id: 'VAL123456789',
    status: 'VALID',
    amount: '499.99',
    bank_tran_id: 'BANK123456789',
    card_type: 'VISA',
    currency: 'BDT'
  }, 'Standard SSL Commerce success callback (with val_id)');

  // Format 2: Success callback without val_id
  await makeRequest('POST', '/payment/success', {
    tran_id: 'TRX_20250618145506_KGUCAA',
    status: 'SUCCESS',
    amount: '499.99',
    bank_tran_id: 'BANK123456789',
    card_type: 'VISA'
  }, 'Success callback without val_id');

  // Format 3: Success callback with minimal data
  await makeRequest('GET', '/payment/success', {
    tran_id: 'TRX_20250618145506_KGUCAA',
    amount: '499.99'
  }, 'Minimal success callback (GET request)');

  // Format 4: Success callback with alternative parameter names
  await makeRequest('POST', '/payment/success', {
    transaction_id: 'TRX_20250618145506_KGUCAA',
    payment_status: 'SUCCESSFUL',
    total_amount: '499.99',
    bank_transaction_id: 'BANK123456789'
  }, 'Alternative parameter names');

  // Format 5: Success callback with store_amount instead of amount
  await makeRequest('POST', '/payment/success', {
    tran_id: 'TRX_20250618145506_KGUCAA',
    status: 'VALID',
    store_amount: '499.99',
    bank_tran_id: 'BANK123456789'
  }, 'Using store_amount parameter');

  // Step 4: Test amount mismatch detection
  console.log('\n📋 STEP 4: Testing Amount Mismatch Detection');
  await makeRequest('POST', '/payment/success', {
    tran_id: 'TRX_20250618145506_KGUCAA',
    status: 'SUCCESS',
    amount: '100.00', // Wrong amount
    bank_tran_id: 'BANK123456789'
  }, 'Amount mismatch test (should fail)');

  // Step 5: Test emergency validation
  console.log('\n📋 STEP 5: Testing Emergency Validation');
  await makeRequest('POST', '/payment/success', {
    tran_id: 'TRX_20250618145506_KGUCAA'
    // No other parameters - should trigger emergency validation
  }, 'Emergency validation (minimal data)');

  // Step 6: Test with real SSL Commerce headers
  console.log('\n📋 STEP 6: Testing with SSL Commerce-like Headers');
  await makeRequest('POST', '/payment/debug', {
    tran_id: 'TRX_20250618145506_KGUCAA',
    status: 'SUCCESS',
    amount: '499.99',
    bank_tran_id: 'BANK123456789',
    APIConnect: 'https://securepay.sslcommerz.com',
    verified_on: new Date().toISOString(),
    processing_type: 'live'
  }, 'SSL Commerce with additional parameters');

  console.log('\n✅ Debug Session Complete!');
  console.log('\n📝 Next Steps:');
  console.log('1. Check the Laravel logs for detailed debugging information');
  console.log('2. Compare the debug output with your actual SSL Commerce callback');
  console.log('3. Look for validation strategy being used and any error messages');
  console.log('4. Verify the payment amount matches exactly');
  console.log('5. Check if the payment record exists in the database');
  
  console.log('\n🔧 Log Commands:');
  console.log('tail -f storage/logs/laravel.log | grep "SSL Commerce"');
  console.log('grep "Payment marked as successful" storage/logs/laravel.log');
  console.log('grep "validation strategy" storage/logs/laravel.log');
}

// Additional utility functions
async function checkSpecificPayment(tranId) {
  console.log(`\n🔍 Checking specific payment: ${tranId}`);
  
  // Check payment status
  await makeRequest('GET', `/payment/status/${tranId}`, null, `Payment status for ${tranId}`);
  
  // Test success callback
  await makeRequest('POST', '/payment/success', {
    tran_id: tranId,
    status: 'SUCCESS',
    amount: '499.99' // You may need to adjust this amount
  }, `Success callback test for ${tranId}`);
}

async function simulateRealSSLCommerceCallback(tranId, amount) {
  console.log(`\n🎭 Simulating real SSL Commerce callback for ${tranId}`);
  
  // This simulates what SSL Commerce might actually send
  const sslCommerceData = {
    tran_id: tranId,
    val_id: '', // Often empty
    status: 'SUCCESS', // or 'VALID'
    amount: amount,
    currency: 'BDT',
    bank_tran_id: `BANK${Date.now()}`,
    card_type: 'VISA-Dutch Bangla',
    store_amount: amount,
    card_no: '424242XXXXXX4242',
    card_issuer: 'STANDARD CHARTERED BANK',
    card_category: 'VISA',
    APIConnect: 'https://securepay.sslcommerz.com/gwprocess/v4/api.php',
    verified_on: new Date().toISOString(),
    processing_type: 'live'
  };

  await makeRequest('POST', '/payment/success', sslCommerceData, 'Real SSL Commerce callback simulation');
}

// Run the debug session
if (process.argv[2] === 'check' && process.argv[3]) {
  checkSpecificPayment(process.argv[3]);
} else if (process.argv[2] === 'simulate' && process.argv[3] && process.argv[4]) {
  simulateRealSSLCommerceCallback(process.argv[3], process.argv[4]);
} else {
  debugSSLCommerceIssue().catch(console.error);
}

console.log('\n💡 Usage:');
console.log('node debug-ssl-commerce.js                    # Run full debug session');
console.log('node debug-ssl-commerce.js check TRX_ID       # Check specific payment');
console.log('node debug-ssl-commerce.js simulate TRX_ID AMOUNT # Simulate SSL Commerce callback');
