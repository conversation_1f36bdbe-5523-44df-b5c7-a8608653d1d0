# Deployment Guide

## Development Setup

1. **Start Laravel Backend**:
   ```bash
   cd bacbon_tutors_v3_backend
   php artisan serve
   ```

2. **Start Frontend Development Server**:
   ```bash
   cd payment-frontend
   npm install
   npm run dev
   ```

3. **Test API Connection**:
   ```bash
   npm run test:api
   ```

## Production Build

1. **Build for Production**:
   ```bash
   npm run build
   ```

2. **Preview Production Build**:
   ```bash
   npm run preview
   ```

## Environment Configuration

### Development
- Frontend: `http://localhost:5173`
- Backend: `http://127.0.0.1:8000`

### Production
Update `.env` file with production API URL:
```
VITE_API_BASE_URL=https://your-domain.com/api
```

## SSL Commerce Configuration

Ensure your Laravel backend has the following environment variables configured:

```env
SSLCOMMERZ_STORE_ID=your_store_id
SSLCOMMERZ_STORE_PASSWORD=your_store_password
SSLCOMMERZ_API_URL=https://securepay.sslcommerz.com/gwprocess/v4/api.php
```

## CORS Configuration

Make sure your Laravel backend allows requests from the frontend domain. Update `config/cors.php`:

```php
'allowed_origins' => [
    'http://localhost:5173',
    'https://your-frontend-domain.com',
],
```

## Deployment Checklist

- [ ] Backend API is running and accessible
- [ ] SSL Commerce credentials are configured
- [ ] CORS is properly configured
- [ ] Frontend environment variables are set
- [ ] Payment redirect URLs are updated in SSL Commerce dashboard
- [ ] Test payment flow end-to-end

## Payment Redirect URLs

Configure these URLs in your SSL Commerce merchant dashboard:

- Success URL: `https://your-domain.com/payment/success`
- Fail URL: `https://your-domain.com/payment/fail`
- Cancel URL: `https://your-domain.com/payment/cancel`
- IPN URL: `https://your-backend-domain.com/api/payment/ipn`
