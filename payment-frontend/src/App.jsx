import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import PackageSelection from './components/PackageSelection';
import PaymentForm from './components/PaymentForm';
import PaymentStatus from './components/PaymentStatus';
import './App.css';

function App() {
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [currentStep, setCurrentStep] = useState(1);

  const handlePackageSelect = (pkg) => {
    setSelectedPackage(pkg);
    setCurrentStep(2);
  };

  const handlePaymentInitiated = () => {
    // This will be called when payment is initiated
    // The actual redirect happens in PaymentForm component
  };

  const resetToPackageSelection = () => {
    setSelectedPackage(null);
    setCurrentStep(1);
  };

  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Routes>
          {/* Main payment flow */}
          <Route path="/" element={
            <div>
              {/* Header */}
              <header className="bg-white shadow-sm border-b">
                <div className="max-w-6xl mx-auto px-6 py-4">
                  <div className="flex items-center justify-between">
                    <h1 className="text-2xl font-bold text-gray-900">BacBon Payment</h1>
                    <div className="flex items-center space-x-4">
                      <div className={`flex items-center ${currentStep >= 1 ? 'text-blue-600' : 'text-gray-400'}`}>
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 1 ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>
                          1
                        </div>
                        <span className="ml-2 hidden sm:block">Select Package</span>
                      </div>
                      <div className="w-8 h-px bg-gray-300"></div>
                      <div className={`flex items-center ${currentStep >= 2 ? 'text-blue-600' : 'text-gray-400'}`}>
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 2 ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>
                          2
                        </div>
                        <span className="ml-2 hidden sm:block">Payment</span>
                      </div>
                    </div>
                  </div>
                </div>
              </header>

              {/* Main content */}
              <main className="py-8">
                {currentStep === 1 && (
                  <PackageSelection
                    onPackageSelect={handlePackageSelect}
                    selectedPackage={selectedPackage}
                  />
                )}

                {currentStep === 2 && (
                  <div>
                    {/* Back button */}
                    <div className="max-w-4xl mx-auto px-6 mb-6">
                      <button
                        onClick={resetToPackageSelection}
                        className="text-blue-600 hover:text-blue-700 flex items-center"
                      >
                        ← Back to Package Selection
                      </button>
                    </div>

                    <PaymentForm
                      selectedPackage={selectedPackage}
                      onPaymentInitiated={handlePaymentInitiated}
                    />
                  </div>
                )}
              </main>

              {/* Footer */}
              <footer className="bg-white border-t mt-12">
                <div className="max-w-6xl mx-auto px-6 py-8">
                  <div className="text-center text-gray-600">
                    <p>&copy; 2024 BacBon. All rights reserved.</p>
                    <p className="mt-2 text-sm">Secure payment powered by SSL Commerce</p>
                  </div>
                </div>
              </footer>
            </div>
          } />

          {/* Payment status routes */}
          <Route path="/payment/success" element={<PaymentStatus type="success" />} />
          <Route path="/payment/fail" element={<PaymentStatus type="fail" />} />
          <Route path="/payment/cancel" element={<PaymentStatus type="cancel" />} />

          {/* Redirect any unknown routes to home */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
