import React, { useState } from 'react';
import { apiEndpoints } from '../services/api';
import { CreditCard, User, Mail, Phone, MapPin, Building, Users, Calendar, Calculator } from 'lucide-react';

const PaymentForm = ({ selectedPackage, onPaymentInitiated }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    organization: '',
    address: '',
    no_of_user: 1,
    month: 1,
    discount: 0,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const calculateTotals = () => {
    const unitPrice = selectedPackage?.price || 0;
    const quantity = formData.month * formData.no_of_user;
    const totalAmount = unitPrice * quantity;
    const discount = parseFloat(formData.discount) || 0;
    const grandTotal = totalAmount - discount;

    return {
      unitPrice,
      quantity,
      totalAmount,
      discount,
      grandTotal
    };
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!selectedPackage) {
      setError('Please select a package first');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const paymentData = {
        ...formData,
        elsa_package_id: selectedPackage.id,
        unit_price: selectedPackage.price,
        no_of_user: parseInt(formData.no_of_user),
        month: parseInt(formData.month),
        discount: parseFloat(formData.discount) || 0,
      };

      const response = await apiEndpoints.initiatePayment(paymentData);
      
      if (response.data.payment_url) {
        // Redirect to SSL Commerce payment gateway
        window.location.href = response.data.payment_url;
      } else {
        setError('Failed to initiate payment. Please try again.');
      }
    } catch (err) {
      setError(err.response?.data?.message || 'Payment initiation failed');
      console.error('Payment error:', err);
    } finally {
      setLoading(false);
    }
  };

  if (!selectedPackage) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-600 mb-4">Please select a package to continue</div>
      </div>
    );
  }

  const totals = calculateTotals();

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h2 className="text-3xl font-bold text-center mb-8">Payment Details</h2>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Payment Form */}
        <div className="bg-white p-6 rounded-lg shadow-lg">
          <h3 className="text-xl font-semibold mb-6 flex items-center">
            <CreditCard className="w-5 h-5 mr-2" />
            Billing Information
          </h3>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <User className="w-4 h-4 inline mr-1" />
                Full Name *
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter your full name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <Mail className="w-4 h-4 inline mr-1" />
                Email Address *
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter your email"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <Phone className="w-4 h-4 inline mr-1" />
                Phone Number *
              </label>
              <input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter your phone number"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <Building className="w-4 h-4 inline mr-1" />
                Organization
              </label>
              <input
                type="text"
                name="organization"
                value={formData.organization}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter your organization name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <MapPin className="w-4 h-4 inline mr-1" />
                Address *
              </label>
              <textarea
                name="address"
                value={formData.address}
                onChange={handleInputChange}
                required
                rows="3"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter your address"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <Users className="w-4 h-4 inline mr-1" />
                  Number of Users *
                </label>
                <input
                  type="number"
                  name="no_of_user"
                  value={formData.no_of_user}
                  onChange={handleInputChange}
                  min="1"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <Calendar className="w-4 h-4 inline mr-1" />
                  Duration (Months) *
                </label>
                <input
                  type="number"
                  name="month"
                  value={formData.month}
                  onChange={handleInputChange}
                  min="1"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <Calculator className="w-4 h-4 inline mr-1" />
                Discount Amount (৳)
              </label>
              <input
                type="number"
                name="discount"
                value={formData.discount}
                onChange={handleInputChange}
                min="0"
                step="0.01"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="0.00"
              />
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                {error}
              </div>
            )}

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Processing...
                </>
              ) : (
                <>
                  <CreditCard className="w-4 h-4 mr-2" />
                  Proceed to Payment
                </>
              )}
            </button>
          </form>
        </div>

        {/* Order Summary */}
        <div className="bg-gray-50 p-6 rounded-lg">
          <h3 className="text-xl font-semibold mb-6">Order Summary</h3>
          
          <div className="space-y-4">
            <div className="bg-white p-4 rounded-lg">
              <h4 className="font-semibold text-lg">{selectedPackage.duration}</h4>
              <p className="text-gray-600">{selectedPackage.description}</p>
              <div className="mt-2">
                <span className="text-2xl font-bold text-blue-600">৳{selectedPackage.price}</span>
                <span className="text-gray-600 ml-2">per month</span>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Unit Price:</span>
                <span>৳{totals.unitPrice}</span>
              </div>
              <div className="flex justify-between">
                <span>Quantity ({formData.no_of_user} users × {formData.month} months):</span>
                <span>{totals.quantity}</span>
              </div>
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span>৳{totals.totalAmount}</span>
              </div>
              {totals.discount > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>Discount:</span>
                  <span>-৳{totals.discount}</span>
                </div>
              )}
              <hr className="my-2" />
              <div className="flex justify-between text-xl font-bold">
                <span>Total:</span>
                <span>৳{totals.grandTotal}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentForm;
