import React, { useState, useEffect } from 'react';
import { apiEndpoints } from '../services/api';
import { Check, Star, Users, Calendar } from 'lucide-react';

const PackageSelection = ({ onPackageSelect, selectedPackage }) => {
  const [categories, setCategories] = useState([]);
  const [packages, setPackages] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    if (selectedCategory) {
      fetchPackages(selectedCategory.id);
    }
  }, [selectedCategory]);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await apiEndpoints.getCategories();
      setCategories(response.data.data || []);
      if (response.data.data && response.data.data.length > 0) {
        setSelectedCategory(response.data.data[0]);
      }
    } catch (err) {
      setError('Failed to fetch categories');
      console.error('Error fetching categories:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchPackages = async (categoryId) => {
    try {
      const response = await apiEndpoints.getPackages(categoryId);
      setPackages(response.data.data || []);
    } catch (err) {
      setError('Failed to fetch packages');
      console.error('Error fetching packages:', err);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">{error}</div>
        <button 
          onClick={fetchCategories}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <h2 className="text-3xl font-bold text-center mb-8">Choose Your Package</h2>
      
      {/* Category Selection */}
      <div className="mb-8">
        <h3 className="text-xl font-semibold mb-4">Select Category</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {categories.map((category) => (
            <div
              key={category.id}
              onClick={() => setSelectedCategory(category)}
              className={`p-4 border rounded-lg cursor-pointer transition-all ${
                selectedCategory?.id === category.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-semibold">{category.title}</h4>
                  <p className="text-sm text-gray-600">{category.subtitle}</p>
                </div>
                {category.badge && (
                  <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                    {category.badge}
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Package Selection */}
      {packages.length > 0 && (
        <div>
          <h3 className="text-xl font-semibold mb-4">Select Package</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {packages.map((pkg) => (
              <div
                key={pkg.id}
                onClick={() => onPackageSelect(pkg)}
                className={`relative p-6 border rounded-xl cursor-pointer transition-all hover:shadow-lg ${
                  selectedPackage?.id === pkg.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200'
                } ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}
              >
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm flex items-center">
                      <Star className="w-4 h-4 mr-1" />
                      Popular
                    </span>
                  </div>
                )}

                <div className="text-center">
                  <h4 className="text-xl font-bold mb-2">{pkg.duration}</h4>
                  
                  <div className="mb-4">
                    {pkg.before_price && pkg.before_price > pkg.price && (
                      <span className="text-gray-500 line-through text-lg">
                        ৳{pkg.before_price}
                      </span>
                    )}
                    <div className="text-3xl font-bold text-blue-600">
                      ৳{pkg.price}
                    </div>
                    <div className="text-sm text-gray-600 flex items-center justify-center mt-1">
                      <Calendar className="w-4 h-4 mr-1" />
                      {pkg.months} month{pkg.months > 1 ? 's' : ''}
                    </div>
                  </div>

                  <p className="text-gray-600 mb-4">{pkg.description}</p>

                  {pkg.features && pkg.features.length > 0 && (
                    <div className="text-left">
                      <h5 className="font-semibold mb-2">Features:</h5>
                      <ul className="space-y-1">
                        {pkg.features.map((feature, index) => (
                          <li key={index} className="flex items-center text-sm">
                            <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {pkg.tag && (
                    <div className="mt-4">
                      <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">
                        {pkg.tag}
                      </span>
                    </div>
                  )}
                </div>

                {selectedPackage?.id === pkg.id && (
                  <div className="absolute top-4 right-4">
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                      <Check className="w-4 h-4 text-white" />
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default PackageSelection;
