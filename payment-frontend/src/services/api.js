import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// API endpoints
export const apiEndpoints = {
  // Categories and Packages
  getCategories: () => api.get('/open/elsa-categories'),
  getPackages: (categoryId) => api.get(`/open/elsa-packages/${categoryId}`),
  
  // Payment
  initiatePayment: (paymentData) => api.post('/payment/pay', paymentData),
  
  // Payment status endpoints (for handling redirects)
  paymentSuccess: (params) => api.get('/payment/success', { params }),
  paymentFail: (params) => api.get('/payment/fail', { params }),
  paymentCancel: (params) => api.get('/payment/cancel', { params }),
};

export default api;
