import { useState } from 'react';

const App = () => {
  const initialData = {
    name: "Dashboarddadfasdf",
    icon: "ph:notification-light",
    url: "dashboard",
    order: 0,
    is_active: 1,
    role_ids: [3],
    description: "",
  };

  const handlePostData = async () => {
    const apiUrl = "http://*************:1212/api/menus";
    const token =
      "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vMTAzLjIwOS40MC44OToxMjEyL2FwaS9sb2dpbiIsImlhdCI6MTczNTE4NTkzNSwiZXhwIjoxNzUwNzM3OTM1LCJuYmYiOjE3MzUxODU5MzUsImp0aSI6IlF0R3RGazFoOEg0NERab04iLCJzdWIiOiIyIiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.XCdzNCc9M81m9XxD2ytqXQ4y0m5AREJlAFO05ADmn1o";

    try {
      const response = await fetch(apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(initialData),
        redirect: 'follow', // Ensures redirects are followed
      });

      if (response.status === 302) {
        // If a redirect occurs, log the location header
        const redirectUrl = response.headers.get("Location");
        console.log("Redirected to:", redirectUrl);
      }

      if (response.ok) {
        // If the request was successful, handle the response
        const result = await response.json();
        alert("Data posted successfully!");
        console.log("Response:", result);
      } else {
        // Handle errors from the API
        const errorData = await response.json();
        alert(errorData.message || "Failed to post data.");
        console.error("Error Response:", errorData);
      }
    } catch (error) {
      // Log any error that occurs during the fetch process
      alert("An error occurred while posting the data.");
      console.error("Error:", error);
    }
  };

  return (
    <div style={{ textAlign: "center", marginTop: "50px" }}>
      <h1>Post Data Example</h1>
      <button
        onClick={handlePostData}
        style={{
          padding: "10px 20px",
          fontSize: "16px",
          cursor: "pointer",
          backgroundColor: "#007BFF",
          color: "white",
          border: "none",
          borderRadius: "5px",
        }}
      >
        Post Data
      </button>
    </div>
  );
};

export default App;
