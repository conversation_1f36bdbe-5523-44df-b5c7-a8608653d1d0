<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('home_carousels', function (Blueprint $table) {
            $table->string('url')->nullable()->after('button_link');
            $table->boolean('has_tutor')->default(false)->after('url');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('home_carousels', function (Blueprint $table) {
            $table->dropColumn('url');
            $table->dropColumn('has_tutor');
        });
    }
};
