<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('elsa_payments', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('organization')->nullable();
            $table->string('address')->nullable();
            $table->integer('no_of_user')->nullable();
            $table->foreignId('elsa_package_id')->nullable();
            $table->integer('month')->nullable();
            $table->decimal('total_amount')->nullable();
            $table->decimal('unit_price')->nullable();
            $table->decimal('discount')->nullable();
            $table->decimal('grand_total')->nullable();
            $table->foreignId('user_id')->nullable();
            $table->string('card_type')->nullable();
            $table->string('currency')->nullable();
            $table->string('tran_id')->nullable();
            $table->string('payment_status')->nullable();
            $table->boolean('is_promo_applied')->default(false);
            $table->foreignId('promo_id')->nullable();
            $table->enum('status', ['pending', 'successful', 'failed', 'cancelled'])->default('pending');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('elsa_payments');
    }
};
