<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('elsa_packages', function (Blueprint $table) {
            $table->id();
            $table->string('duration')->default("1 Year Plan");
            $table->decimal('before_price', 8, 2)->default(3);
            $table->decimal('price', 8, 2)->default(3);
            $table->boolean('popular')->default(true);
            $table->text('description');
            $table->integer('months')->default(12);
            $table->enum('billing_type', ['monthly', 'yearly', 'daily'])->default('monthly');
            $table->string('tag')->nullable();
            $table->foreignId('category_id')->nullable();
            $table->string('button_text')->nullable();
            $table->string('button_type')->nullable();
            $table->boolean('is_active')->default(true);
            $table->json('features')->nullable();
            $table->timestamps();
        });
        
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('packages');
    }
};
