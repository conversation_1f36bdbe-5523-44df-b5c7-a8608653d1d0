services:
  app:
    build: .
    container_name: bacbon_tutors_renovation_app
    restart: always
    working_dir: /var/www/html
    volumes:
      - .:/var/www/html
    networks:
      - bacbon_tutors_v3

  nginx:
    image: nginx:alpine
    container_name: nginx_server
    restart: always
    ports:
      - "8080:80"
    volumes:
      - .:/var/www/html
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
    networks:
      - bacbon_tutors_v3

networks:
  bacbon_tutors_v3:
