<?php

namespace App\Http\Controllers;

use App\Http\Traits\HelperTrait;
use App\Services\DashboardService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class DashboardController extends Controller
{
    use HelperTrait;
    private $service;
    public function __construct(DashboardService $service)
    {
        $this->service = $service;
    }


    public function adminDashboard(Request $request)
    {
        try {
            $data = $this->service->adminDashboard($request);
            return $this->successResponse($data, 'Data retrieved successfully', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get daily job statistics for a specific month
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function dailyJobStats(Request $request)
    {
        try {
            $data = $this->service->getDailyJobStats($request);
            return $this->successResponse($data, 'Daily job statistics retrieved successfully', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
