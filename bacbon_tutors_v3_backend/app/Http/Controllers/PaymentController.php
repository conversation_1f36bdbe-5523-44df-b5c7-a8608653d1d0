<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;
use App\Models\ElsaPayment;
use App\Http\Requests\StorePaymentRequest;

class PaymentController extends Controller
{
    public function pay(StorePaymentRequest $request)
    {
        $unitPrice   = $request->unit_price;
        $quantity    = $request->month * $request->no_of_user;
        $totalAmount = $unitPrice * $quantity;
        $discount    = $request->discount ?? 0;
        $grandTotal  = $totalAmount - $discount;

        $transactionId = 'TRX_' . Carbon::now()->format('YmdHis') . '_' . Str::upper(Str::random(6));

        $payment = new ElsaPayment();
        $payment->fill([
            'name'            => $request->name,
            'email'           => $request->email,
            'phone'           => $request->phone,
            'organization'    => $request->organization,
            'address'         => $request->address,
            'no_of_user'      => $request->no_of_user,
            'elsa_package_id' => $request->elsa_package_id,
            'month'           => $request->month,
            'total_amount'    => $totalAmount,
            'unit_price'      => $unitPrice,
            'discount'        => $discount,
            'grand_total'     => $grandTotal,
            'currency'        => 'BDT',
            'tran_id'         => $transactionId,
            'payment_status'  => 'pending',
            'user_id'         => auth()->id(),
        ]);
        $payment->save();

        $data = [
            'store_id'        => env('SSLCOMMERZ_STORE_ID'),
            'store_passwd'    => env('SSLCOMMERZ_STORE_PASSWORD'),
            'total_amount'    => $grandTotal,
            'currency'        => 'BDT',
            'tran_id'         => $transactionId,
            'success_url'     => url('/api/payment/success'),
            'fail_url'        => url('/api/payment/fail'),
            'cancel_url'      => url('/api/payment/cancel'),
            'ipn_url'         => url('/api/payment/ipn'),
            'cus_name'        => $request->name,
            'cus_email'       => $request->email,
            'cus_add1'        => $request->address,
            'cus_city'        => 'Dhaka',
            'cus_postcode'    => '1216',
            'cus_country'     => 'Bangladesh',
            'cus_phone'       => $request->phone,
            'shipping_method'  => 'NO',
            'product_name'     => 'Premium Subscription',
            'product_category' => 'Digital',
            'product_profile'  => 'general',
        ];

        $response = Http::asForm()->post(env('SSLCOMMERZ_API_URL'), $data);
        if ($response['status'] === 'SUCCESS' && isset($response['GatewayPageURL'])) {
            return response()->json([
                'payment_url' => $response['GatewayPageURL'],
                'transaction_id' => $transactionId,
            ]);
        }

        return response()->json([
            'message' => 'Payment initiation failed',
            'response' => $response->json()
        ], 422);
    }

    public function success(Request $request)
    {
        try {
            // Handle both GET and POST requests from SSL Commerce
            $tranId = $request->input('tran_id');
            $valId = $request->input('val_id');
            $amount = $request->input('amount');
            $status = $request->input('status');
            $bankTranId = $request->input('bank_tran_id');
            $cardType = $request->input('card_type');

            // Log the incoming request for debugging
            \Log::info('SSL Commerce Success Callback', [
                'tran_id' => $tranId,
                'val_id' => $valId,
                'amount' => $amount,
                'status' => $status,
                'bank_tran_id' => $bankTranId,
                'card_type' => $cardType,
                'all_params' => $request->all(),
                'method' => $request->method(),
                'user_agent' => $request->header('User-Agent')
            ]);

            // Validate required parameters
            if (!$tranId) {
                \Log::error('SSL Commerce Success: Missing transaction ID', [
                    'all_params' => $request->all()
                ]);
                return $this->handlePaymentError($request, 'Transaction ID is required but was not provided by SSL Commerce', null);
            }

            // Find payment record
            $payment = ElsaPayment::where('tran_id', $tranId)->first();
            if (!$payment) {
                \Log::error('SSL Commerce Success: Payment record not found in database', [
                    'tran_id' => $tranId,
                    'searched_table' => 'elsa_payments'
                ]);
                return $this->handlePaymentError($request, "Payment record not found for transaction ID: {$tranId}", $tranId);
            }

            // Check if payment is already processed
            if ($payment->status === 'successful') {
                \Log::info('SSL Commerce Success: Payment already processed successfully', [
                    'tran_id' => $tranId,
                    'current_status' => $payment->status,
                    'payment_status' => $payment->payment_status
                ]);
                return $this->handlePaymentSuccess($request, $payment, [
                    'status' => 'VALID',
                    'amount' => $payment->grand_total,
                    'val_id' => $valId,
                    'bank_tran_id' => $payment->bank_tran_id,
                    'card_type' => $payment->card_type,
                    'message' => 'Payment already processed successfully'
                ]);
            }

            // Determine validation strategy based on available parameters
            $validationStrategy = $this->determineValidationStrategy($valId, $status, $amount, $bankTranId);

            \Log::info('SSL Commerce Success: Using validation strategy', [
                'tran_id' => $tranId,
                'strategy' => $validationStrategy,
                'has_val_id' => !empty($valId),
                'has_status' => !empty($status),
                'has_amount' => !empty($amount),
                'has_bank_tran_id' => !empty($bankTranId)
            ]);

            switch ($validationStrategy) {
                case 'full_validation':
                    return $this->performFullValidation($request, $payment, $valId, $tranId, $amount);

                case 'status_based_validation':
                    return $this->performStatusBasedValidation($request, $payment, $status, $amount, $bankTranId, $cardType, $tranId);

                case 'minimal_validation':
                    return $this->performMinimalValidation($request, $payment, $amount, $bankTranId, $cardType, $tranId);

                default:
                    \Log::error('SSL Commerce Success: Insufficient data for validation', [
                        'tran_id' => $tranId,
                        'available_params' => array_filter([
                            'val_id' => $valId,
                            'status' => $status,
                            'amount' => $amount,
                            'bank_tran_id' => $bankTranId
                        ])
                    ]);
                    return $this->handlePaymentError($request, 'Insufficient payment data received from SSL Commerce. Missing validation ID, status, and amount.', $tranId);
            }

        } catch (\Exception $e) {
            \Log::error('SSL Commerce Success: Exception occurred', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);
            return $this->handlePaymentError($request, 'Internal server error', $request->input('tran_id'));
        }
    }

    public function fail(Request $request)
    {
        // Handle both GET and POST requests from SSL Commerce
        $tranId = $request->input('tran_id');

        $payment = ElsaPayment::where('tran_id', $tranId)->first();

        if ($payment) {
            $payment->update([
                'status'         => 'failed',
                'payment_status' => $request->input('status') ?? 'FAILED',
            ]);
        }

        // Check if this is a redirect from SSL Commerce (browser request)
        if ($request->hasHeader('User-Agent') && !$request->wantsJson()) {
            // Redirect to frontend fail page with parameters
            $frontendUrl = env('FRONTEND_URL', 'http://localhost:5174');
            $params = http_build_query([
                'tran_id' => $tranId,
                'status' => $request->input('status') ?? 'FAILED',
            ]);
            return redirect($frontendUrl . '/payment/fail?' . $params);
        }

        return response()->json([
            'message' => 'Payment Failed',
            'transaction_id' => $tranId,
        ]);
    }

    public function cancel(Request $request)
    {
        // Handle both GET and POST requests from SSL Commerce
        $tranId = $request->input('tran_id');

        $payment = ElsaPayment::where('tran_id', $tranId)->first();

        if ($payment) {
            $payment->update([
                'status'         => 'cancelled',
                'payment_status' => $request->input('status') ?? 'CANCELLED',
            ]);
        }

        // Check if this is a redirect from SSL Commerce (browser request)
        if ($request->hasHeader('User-Agent') && !$request->wantsJson()) {
            // Redirect to frontend cancel page with parameters
            $frontendUrl = env('FRONTEND_URL', 'http://localhost:5174');
            $params = http_build_query([
                'tran_id' => $tranId,
                'status' => $request->input('status') ?? 'CANCELLED',
            ]);
            return redirect($frontendUrl . '/payment/cancel?' . $params);
        }

        return response()->json([
            'message' => 'Payment Cancelled',
            'transaction_id' => $tranId,
        ]);
    }

    public function ipn(Request $request)
    {
        try {
            // IPN is always POST, but use input() for consistency
            $tranId = $request->input('tran_id');
            $valId = $request->input('val_id');

            \Log::info('SSL Commerce IPN Received', [
                'tran_id' => $tranId,
                'val_id' => $valId,
                'all_params' => $request->all()
            ]);

            if (!$tranId || !$valId) {
                \Log::error('SSL Commerce IPN: Missing required parameters', [
                    'tran_id' => $tranId,
                    'val_id' => $valId
                ]);
                return response()->json(['message' => 'Invalid IPN data.'], 400);
            }

            $payment = ElsaPayment::where('tran_id', $tranId)->first();

            if (!$payment) {
                \Log::error('SSL Commerce IPN: Payment not found', ['tran_id' => $tranId]);
                return response()->json(['message' => 'Payment not found.'], 404);
            }

            if ($payment->status === 'successful') {
                \Log::info('SSL Commerce IPN: Payment already processed successfully', ['tran_id' => $tranId]);
                return response()->json(['message' => 'Payment already processed successfully.'], 200);
            }

            if ($payment->status === 'failed') {
                \Log::info('SSL Commerce IPN: Payment already marked as failed', ['tran_id' => $tranId]);
                return response()->json(['message' => 'Payment already marked as failed.'], 200);
            }

            // Validate with SSL Commerce
            $validationResult = $this->validateWithSSLCommerz($valId);

            if ($validationResult['success']) {
                $data = $validationResult['data'];

                // Verify transaction ID and amount
                if (isset($data['tran_id']) && $data['tran_id'] !== $tranId) {
                    \Log::error('SSL Commerce IPN: Transaction ID mismatch', [
                        'expected' => $tranId,
                        'received' => $data['tran_id']
                    ]);
                    return response()->json(['message' => 'Transaction ID mismatch.'], 422);
                }

                // Verify amount
                $expectedAmount = (float) $payment->grand_total;
                $receivedAmount = (float) ($data['amount'] ?? 0);

                if (abs($expectedAmount - $receivedAmount) > 0.01) {
                    \Log::error('SSL Commerce IPN: Amount mismatch', [
                        'expected' => $expectedAmount,
                        'received' => $receivedAmount
                    ]);
                    return response()->json(['message' => 'Amount mismatch.'], 422);
                }

                // Update payment as successful
                $payment->update([
                    'status' => 'successful',
                    'payment_status' => $data['status'],
                    'val_id' => $data['val_id'],
                    'card_type' => $data['card_type'] ?? null,
                    'store_amount' => $data['store_amount'] ?? $data['amount'] ?? null,
                    'bank_tran_id' => $data['bank_tran_id'] ?? null,
                    'transaction_date' => $data['tran_date'] ?? now(),
                ]);

                \Log::info('SSL Commerce IPN: Payment processed successfully', [
                    'tran_id' => $tranId,
                    'amount' => $payment->grand_total
                ]);

                return response()->json(['message' => 'IPN Processed Successfully'], 200);
            } else {
                \Log::error('SSL Commerce IPN: Validation failed', [
                    'tran_id' => $tranId,
                    'error' => $validationResult['error']
                ]);

                $payment->update([
                    'status' => 'failed',
                    'payment_status' => 'IPN_VALIDATION_FAILED',
                ]);

                return response()->json(['message' => 'IPN validation failed: ' . $validationResult['error']], 422);
            }

        } catch (\Exception $e) {
            \Log::error('SSL Commerce IPN: Exception occurred', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);

            return response()->json(['message' => 'Internal server error'], 500);
        }
    }

    /**
     * Determine the best validation strategy based on available parameters
     */
    private function determineValidationStrategy($valId, $status, $amount, $bankTranId)
    {
        // Strategy 1: Full validation with val_id
        if (!empty($valId)) {
            return 'full_validation';
        }

        // Strategy 2: Status-based validation (common SSL Commerce configuration)
        if (!empty($status) && in_array(strtoupper($status), ['VALID', 'VALIDATED', 'SUCCESS', 'SUCCESSFUL'])) {
            return 'status_based_validation';
        }

        // Strategy 3: Minimal validation with amount and bank transaction ID
        if (!empty($amount) || !empty($bankTranId)) {
            return 'minimal_validation';
        }

        // No sufficient data for validation
        return 'insufficient_data';
    }

    /**
     * Perform full validation using SSL Commerce API with val_id
     */
    private function performFullValidation($request, $payment, $valId, $tranId, $amount)
    {
        \Log::info('SSL Commerce Success: Performing full validation with val_id', [
            'tran_id' => $tranId,
            'val_id' => $valId
        ]);

        $validationResult = $this->validateWithSSLCommerz($valId);

        if ($validationResult['success']) {
            $validationData = $validationResult['data'];

            // Verify transaction ID matches
            if (isset($validationData['tran_id']) && $validationData['tran_id'] !== $tranId) {
                \Log::error('SSL Commerce Success: Transaction ID mismatch in full validation', [
                    'expected' => $tranId,
                    'received' => $validationData['tran_id']
                ]);
                return $this->handlePaymentError($request, "Transaction ID mismatch. Expected: {$tranId}, Received: {$validationData['tran_id']}", $tranId);
            }

            // Verify amount matches
            $expectedAmount = (float) $payment->grand_total;
            $receivedAmount = (float) ($validationData['amount'] ?? $amount ?? 0);

            if (abs($expectedAmount - $receivedAmount) > 0.01) {
                \Log::error('SSL Commerce Success: Amount mismatch in full validation', [
                    'expected' => $expectedAmount,
                    'received' => $receivedAmount,
                    'difference' => abs($expectedAmount - $receivedAmount)
                ]);
                return $this->handlePaymentError($request, "Payment amount mismatch. Expected: {$expectedAmount} BDT, Received: {$receivedAmount} BDT", $tranId);
            }

            // Payment is valid - update record
            return $this->handlePaymentSuccess($request, $payment, $validationData);
        } else {
            \Log::error('SSL Commerce Success: Full validation failed', [
                'tran_id' => $tranId,
                'val_id' => $valId,
                'error' => $validationResult['error']
            ]);
            return $this->handlePaymentError($request, 'SSL Commerce API validation failed: ' . $validationResult['error'], $tranId);
        }
    }

    /**
     * Perform status-based validation when val_id is not available
     */
    private function performStatusBasedValidation($request, $payment, $status, $amount, $bankTranId, $cardType, $tranId)
    {
        \Log::info('SSL Commerce Success: Performing status-based validation', [
            'tran_id' => $tranId,
            'status' => $status,
            'amount' => $amount,
            'bank_tran_id' => $bankTranId
        ]);

        // Verify amount if provided
        if ($amount) {
            $expectedAmount = (float) $payment->grand_total;
            $receivedAmount = (float) $amount;

            if (abs($expectedAmount - $receivedAmount) > 0.01) {
                \Log::error('SSL Commerce Success: Amount mismatch in status-based validation', [
                    'expected' => $expectedAmount,
                    'received' => $receivedAmount,
                    'difference' => abs($expectedAmount - $receivedAmount)
                ]);
                return $this->handlePaymentError($request, "Payment amount mismatch. Expected: {$expectedAmount} BDT, Received: {$receivedAmount} BDT", $tranId);
            }
        }

        // Accept as successful payment with available data
        return $this->handlePaymentSuccess($request, $payment, [
            'status' => 'VALID',
            'amount' => $amount ?? $payment->grand_total,
            'val_id' => null,
            'bank_tran_id' => $bankTranId,
            'card_type' => $cardType,
            'tran_id' => $tranId,
            'validation_method' => 'status_based'
        ]);
    }

    /**
     * Perform minimal validation with basic parameters
     */
    private function performMinimalValidation($request, $payment, $amount, $bankTranId, $cardType, $tranId)
    {
        \Log::warning('SSL Commerce Success: Performing minimal validation (limited SSL Commerce data)', [
            'tran_id' => $tranId,
            'amount' => $amount,
            'bank_tran_id' => $bankTranId,
            'note' => 'This payment will be marked as successful but requires manual verification'
        ]);

        // Verify amount if provided
        if ($amount) {
            $expectedAmount = (float) $payment->grand_total;
            $receivedAmount = (float) $amount;

            if (abs($expectedAmount - $receivedAmount) > 0.01) {
                \Log::error('SSL Commerce Success: Amount mismatch in minimal validation', [
                    'expected' => $expectedAmount,
                    'received' => $receivedAmount,
                    'difference' => abs($expectedAmount - $receivedAmount)
                ]);
                return $this->handlePaymentError($request, "Payment amount mismatch. Expected: {$expectedAmount} BDT, Received: {$receivedAmount} BDT", $tranId);
            }
        }

        // Accept payment but mark for manual verification
        return $this->handlePaymentSuccess($request, $payment, [
            'status' => 'VALID',
            'amount' => $amount ?? $payment->grand_total,
            'val_id' => null,
            'bank_tran_id' => $bankTranId,
            'card_type' => $cardType,
            'tran_id' => $tranId,
            'validation_method' => 'minimal',
            'requires_manual_verification' => true
        ]);
    }

    /**
     * Validate payment with SSL Commerce API
     */
    private function validateWithSSLCommerz($valId)
    {
        try {
            $response = Http::timeout(30)->asForm()->get('https://securepay.sslcommerz.com/validator/api/validationserverAPI.php', [
                'val_id' => $valId,
                'store_id' => env('SSLCOMMERZ_STORE_ID'),
                'store_passwd' => env('SSLCOMMERZ_STORE_PASSWORD'),
                'v' => 1,
                'format' => 'json',
            ]);

            if (!$response->successful()) {
                return [
                    'success' => false,
                    'error' => 'SSL Commerce API request failed: ' . $response->status()
                ];
            }

            $data = $response->json();

            if (!$data) {
                return [
                    'success' => false,
                    'error' => 'Invalid response from SSL Commerce API'
                ];
            }

            if (isset($data['status']) && $data['status'] === 'VALID') {
                return [
                    'success' => true,
                    'data' => $data
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'Payment validation failed: ' . ($data['status'] ?? 'Unknown status'),
                    'data' => $data
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'SSL Commerce validation exception: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Handle successful payment
     */
    private function handlePaymentSuccess($request, $payment, $validationData)
    {
        // Prepare payment status based on validation method
        $paymentStatus = $validationData['status'] ?? 'VALID';
        if (isset($validationData['validation_method'])) {
            $paymentStatus .= '_' . strtoupper($validationData['validation_method']);
        }

        // Update payment record
        $updateData = [
            'status' => 'successful',
            'payment_status' => $paymentStatus,
            'val_id' => $validationData['val_id'] ?? null,
            'card_type' => $validationData['card_type'] ?? null,
            'store_amount' => $validationData['store_amount'] ?? $validationData['amount'] ?? null,
            'bank_tran_id' => $validationData['bank_tran_id'] ?? null,
            'transaction_date' => $validationData['tran_date'] ?? now(),
        ];

        $payment->update($updateData);

        $logData = [
            'tran_id' => $payment->tran_id,
            'amount' => $payment->grand_total,
            'validation_method' => $validationData['validation_method'] ?? 'full',
            'payment_status' => $paymentStatus,
            'updated_fields' => $updateData
        ];

        if (isset($validationData['requires_manual_verification'])) {
            $logData['manual_verification_required'] = true;
            \Log::warning('Payment marked as successful but requires manual verification', $logData);
        } else {
            \Log::info('Payment marked as successful', $logData);
        }

        // Check if this is a browser redirect from SSL Commerce
        if ($request->hasHeader('User-Agent') && !$request->wantsJson()) {
            $frontendUrl = env('FRONTEND_URL', 'http://localhost:5174');
            $params = http_build_query([
                'tran_id' => $payment->tran_id,
                'val_id' => $validationData['val_id'] ?? '',
                'amount' => $validationData['amount'] ?? $payment->grand_total,
                'status' => $validationData['status'] ?? 'VALID',
                'bank_tran_id' => $validationData['bank_tran_id'] ?? '',
                'card_type' => $validationData['card_type'] ?? ''
            ]);
            return redirect($frontendUrl . '/payment/success?' . $params);
        }

        // Return JSON response for API calls
        return response()->json([
            'message' => $validationData['message'] ?? 'Payment Successful & Validated',
            'transaction_id' => $payment->tran_id,
            'amount' => $validationData['amount'] ?? $payment->grand_total,
            'currency' => $validationData['currency'] ?? 'BDT',
            'bank_tran_id' => $validationData['bank_tran_id'] ?? null,
            'payment_status' => $validationData['status'] ?? 'VALID',
            'card_type' => $validationData['card_type'] ?? null,
            'transaction_date' => $validationData['tran_date'] ?? now()->toISOString(),
        ]);
    }

    /**
     * Handle payment error
     */
    private function handlePaymentError($request, $errorMessage, $tranId = null)
    {
        // Update payment record if transaction ID is available
        if ($tranId) {
            $payment = ElsaPayment::where('tran_id', $tranId)->first();
            if ($payment && $payment->status !== 'failed') {
                $payment->update([
                    'status' => 'failed',
                    'payment_status' => 'VALIDATION_FAILED',
                ]);
            }
        }

        // Check if this is a browser redirect from SSL Commerce
        if ($request->hasHeader('User-Agent') && !$request->wantsJson()) {
            $frontendUrl = env('FRONTEND_URL', 'http://localhost:5174');
            $params = http_build_query([
                'tran_id' => $tranId ?? '',
                'message' => $errorMessage,
                'error' => 'validation_failed'
            ]);
            return redirect($frontendUrl . '/payment/fail?' . $params);
        }

        // Return JSON response for API calls
        return response()->json([
            'message' => $errorMessage,
            'transaction_id' => $tranId,
            'error' => 'validation_failed'
        ], 422);
    }
}
