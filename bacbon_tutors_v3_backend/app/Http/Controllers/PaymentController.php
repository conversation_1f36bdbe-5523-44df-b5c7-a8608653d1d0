<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Carbon\Carbon;
use App\Models\ElsaPayment;
use App\Http\Requests\StorePaymentRequest;

class PaymentController extends Controller
{
    public function pay(StorePaymentRequest $request)
    {
        $unitPrice   = $request->unit_price;
        $quantity    = $request->month * $request->no_of_user;
        $totalAmount = $unitPrice * $quantity;
        $discount    = $request->discount ?? 0;
        $grandTotal  = $totalAmount - $discount;

        $transactionId = 'TRX_' . Carbon::now()->format('YmdHis') . '_' . Str::upper(Str::random(6));

        $payment = new ElsaPayment();
        $payment->fill([
            'name'            => $request->name,
            'email'           => $request->email,
            'phone'           => $request->phone,
            'organization'    => $request->organization,
            'address'         => $request->address,
            'no_of_user'      => $request->no_of_user,
            'elsa_package_id' => $request->elsa_package_id,
            'month'           => $request->month,
            'total_amount'    => $totalAmount,
            'unit_price'      => $unitPrice,
            'discount'        => $discount,
            'grand_total'     => $grandTotal,
            'currency'        => 'BDT',
            'tran_id'         => $transactionId,
            'payment_status'  => 'pending',
            'user_id'         => auth()->id(),
        ]);
        $payment->save();

        $data = [
            'store_id'        => env('SSLCOMMERZ_STORE_ID'),
            'store_passwd'    => env('SSLCOMMERZ_STORE_PASSWORD'),
            'total_amount'    => $grandTotal,
            'currency'        => 'BDT',
            'tran_id'         => $transactionId,
            'success_url'     => url('/api/payment/success'),
            'fail_url'        => url('/api/payment/fail'),
            'cancel_url'      => url('/api/payment/cancel'),
            'ipn_url'         => url('/api/payment/ipn'),
            'cus_name'        => $request->name,
            'cus_email'       => $request->email,
            'cus_add1'        => $request->address,
            'cus_city'        => 'Dhaka',
            'cus_postcode'    => '1216',
            'cus_country'     => 'Bangladesh',
            'cus_phone'       => $request->phone,
            'shipping_method'  => 'NO',
            'product_name'     => 'Premium Subscription',
            'product_category' => 'Digital',
            'product_profile'  => 'general',
        ];

        $response = Http::asForm()->post(env('SSLCOMMERZ_API_URL'), $data);
        if ($response['status'] === 'SUCCESS' && isset($response['GatewayPageURL'])) {
            return response()->json([
                'payment_url' => $response['GatewayPageURL'],
                'transaction_id' => $transactionId,
            ]);
        }

        return response()->json([
            'message' => 'Payment initiation failed',
            'response' => $response->json()
        ], 422);
    }

    public function success(Request $request)
    {
        $tranId = $request->tran_id;
        $valId  = $request->val_id;
        $payment = ElsaPayment::where('tran_id', $tranId)->first();

        if (!$payment) {
            return response()->json(['message' => 'Payment not found.'], 404);
        }

        // 🔍 Call SSLCOMMERZ Validation API
        $response = Http::asForm()->get('https://securepay.sslcommerz.com/validator/api/validationserverAPI.php', [
            'val_id'       => $valId,
            'store_id'     => env('SSLCOMMERZ_STORE_ID'),
            'store_passwd' => env('SSLCOMMERZ_STORE_PASSWORD'),
            'v'            => 1,
            'format'       => 'json',
        ]);

        // ✅ Handle successful API response
        if ($response->successful()) {
            $data = $response->json();
            if (isset($data['status']) && $data['status'] === 'VALID') {
                $payment->update([
                    'status'            => 'successful',
                    'payment_status'    => $data['status'],
                    'val_id'            => $data['val_id'],
                    'card_type'         => $data['card_type'] ?? null,
                    'store_amount'      => $data['store_amount'] ?? null,
                    'bank_tran_id'      => $data['bank_tran_id'] ?? null,
                    'transaction_date'  => $data['tran_date'] ?? null,
                ]);

                return response()->json([
                    'message'         => 'Payment Successful & Validated',
                    'transaction_id'  => $tranId,
                    'amount'          => $data['amount'] ?? $payment->grand_total,
                    'currency'        => $data['currency'] ?? 'BDT',
                    'bank_tran_id'    => $data['bank_tran_id'] ?? null,
                    'payment_status'  => $data['status'],
                    'card_type'       => $data['card_type'] ?? null,
                ]);
            }
        }

        // ❌ If validation failed or unexpected response
        $payment->update([
            'status'         => 'failed',
            'payment_status' => 'VALIDATION_FAILED',
        ]);

        return response()->json([
            'message'         => 'Payment validation failed',
            'transaction_id'  => $tranId,
        ], 422);
    }

    public function fail(Request $request)
    {
        $tranId = $request->tran_id;

        $payment = ElsaPayment::where('tran_id', $tranId)->first();

        if ($payment) {
            $payment->update([
                'status'         => 'failed',
                'payment_status' => $request->status ?? 'FAILED',
            ]);
        }

        return response()->json([
            'message' => 'Payment Failed',
            'transaction_id' => $tranId,
        ]);
    }

    public function cancel(Request $request)
    {
        $tranId = $request->tran_id;

        $payment = ElsaPayment::where('tran_id', $tranId)->first();

        if ($payment) {
            $payment->update([
                'status'         => 'cancelled',
                'payment_status' => $request->status ?? 'CANCELLED',
            ]);
        }

        return response()->json([
            'message' => 'Payment Cancelled',
            'transaction_id' => $tranId,
        ]);
    }

    public function ipn(Request $request)
    {
        $tranId = $request->tran_id;
        $valId  = $request->val_id;

        if (!$tranId || !$valId) {
            return response()->json(['message' => 'Invalid IPN data.'], 400);
        }

        $payment = ElsaPayment::where('tran_id', $tranId)->first();

        if (!$payment) {
            return response()->json(['message' => 'Payment not found.'], 404);
        }

        if ($payment->status !== 'pending') {
            return response()->json(['message' => 'Payment already processed.'], 200);
        }

        $response = Http::asForm()->get('https://securepay.sslcommerz.com/validator/api/validationserverAPI.php', [
            'val_id'       => $valId,
            'store_id'     => env('SSLCOMMERZ_STORE_ID'),
            'store_passwd' => env('SSLCOMMERZ_STORE_PASSWORD'),
            'v'            => 1,
            'format'       => 'json',
        ]);

        if ($response->successful() && $response['status'] === 'VALID') {
            $data = $response->json();

            $payment->update([
                'status'            => 'successful',
                'payment_status'    => $data['status'],
                'val_id'            => $data['val_id'],
                'card_type'         => $data['card_type'] ?? null,
                'store_amount'      => $data['store_amount'] ?? null,
                'bank_tran_id'      => $data['bank_tran_id'] ?? null,
                'transaction_date'  => $data['tran_date'] ?? null,
            ]);

            return response()->json(['message' => 'IPN Processed Successfully'], 200);
        }

        $payment->update([
            'status'         => 'failed',
            'payment_status' => 'IPN_VALIDATION_FAILED',
        ]);

        return response()->json(['message' => 'IPN validation failed'], 422);
    }
}
