<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;
use App\Models\ElsaPayment;
use App\Http\Requests\StorePaymentRequest;

class PaymentController extends Controller
{
    public function pay(StorePaymentRequest $request)
    {
        $unitPrice   = $request->unit_price;
        $quantity    = $request->month * $request->no_of_user;
        $totalAmount = $unitPrice * $quantity;
        $discount    = $request->discount ?? 0;
        $grandTotal  = $totalAmount - $discount;

        $transactionId = 'TRX_' . Carbon::now()->format('YmdHis') . '_' . Str::upper(Str::random(6));

        $payment = new ElsaPayment();
        $payment->fill([
            'name'            => $request->name,
            'email'           => $request->email,
            'phone'           => $request->phone,
            'organization'    => $request->organization,
            'address'         => $request->address,
            'no_of_user'      => $request->no_of_user,
            'elsa_package_id' => $request->elsa_package_id,
            'month'           => $request->month,
            'total_amount'    => $totalAmount,
            'unit_price'      => $unitPrice,
            'discount'        => $discount,
            'grand_total'     => $grandTotal,
            'currency'        => 'BDT',
            'tran_id'         => $transactionId,
            'payment_status'  => 'pending',
            'user_id'         => auth()->id(),
        ]);
        $payment->save();

        $data = [
            'store_id'        => env('SSLCOMMERZ_STORE_ID'),
            'store_passwd'    => env('SSLCOMMERZ_STORE_PASSWORD'),
            'total_amount'    => $grandTotal,
            'currency'        => 'BDT',
            'tran_id'         => $transactionId,
            'success_url'     => url('/api/payment/success'),
            'fail_url'        => url('/api/payment/fail'),
            'cancel_url'      => url('/api/payment/cancel'),
            'ipn_url'         => url('/api/payment/ipn'),
            'cus_name'        => $request->name,
            'cus_email'       => $request->email,
            'cus_add1'        => $request->address,
            'cus_city'        => 'Dhaka',
            'cus_postcode'    => '1216',
            'cus_country'     => 'Bangladesh',
            'cus_phone'       => $request->phone,
            'shipping_method'  => 'NO',
            'product_name'     => 'Premium Subscription',
            'product_category' => 'Digital',
            'product_profile'  => 'general',
        ];

        $response = Http::asForm()->post(env('SSLCOMMERZ_API_URL'), $data);
        if ($response['status'] === 'SUCCESS' && isset($response['GatewayPageURL'])) {
            return response()->json([
                'payment_url' => $response['GatewayPageURL'],
                'transaction_id' => $transactionId,
            ]);
        }

        return response()->json([
            'message' => 'Payment initiation failed',
            'response' => $response->json()
        ], 422);
    }

    public function success(Request $request)
    {
        try {
            // Enhanced parameter extraction - check multiple possible parameter names
            $tranId = $request->input('tran_id') ?? $request->input('transaction_id') ?? $request->input('trans_id');
            $valId = $request->input('val_id') ?? $request->input('validation_id');
            $amount = $request->input('amount') ?? $request->input('store_amount') ?? $request->input('total_amount');
            $status = $request->input('status') ?? $request->input('payment_status') ?? $request->input('transaction_status');
            $bankTranId = $request->input('bank_tran_id') ?? $request->input('bank_transaction_id');
            $cardType = $request->input('card_type') ?? $request->input('card_brand');

            // Additional SSL Commerce parameters that might be present
            $currency = $request->input('currency');
            $storeAmount = $request->input('store_amount');
            $cardNo = $request->input('card_no');
            $cardIssuer = $request->input('card_issuer');
            $cardCategory = $request->input('card_category');
            $apiConnectUrl = $request->input('APIConnect');
            $verifiedOn = $request->input('verified_on');
            $processingType = $request->input('processing_type');

            // Comprehensive logging for debugging
            \Log::info('SSL Commerce Success Callback - ENHANCED DEBUG', [
                'extracted_params' => [
                    'tran_id' => $tranId,
                    'val_id' => $valId,
                    'amount' => $amount,
                    'status' => $status,
                    'bank_tran_id' => $bankTranId,
                    'card_type' => $cardType,
                    'currency' => $currency,
                    'store_amount' => $storeAmount,
                ],
                'additional_ssl_params' => [
                    'card_no' => $cardNo,
                    'card_issuer' => $cardIssuer,
                    'card_category' => $cardCategory,
                    'api_connect_url' => $apiConnectUrl,
                    'verified_on' => $verifiedOn,
                    'processing_type' => $processingType,
                ],
                'request_details' => [
                    'method' => $request->method(),
                    'user_agent' => $request->header('User-Agent'),
                    'content_type' => $request->header('Content-Type'),
                    'ip_address' => $request->ip(),
                    'url' => $request->fullUrl(),
                ],
                'all_raw_params' => $request->all(),
                'query_params' => $request->query(),
                'post_params' => $request->request->all(),
                'headers' => $request->headers->all(),
                'timestamp' => now()->toISOString()
            ]);

            // Validate required parameters with enhanced debugging
            if (!$tranId) {
                \Log::error('SSL Commerce Success: Missing transaction ID - CRITICAL DEBUG', [
                    'all_params' => $request->all(),
                    'checked_param_names' => ['tran_id', 'transaction_id', 'trans_id'],
                    'param_values' => [
                        'tran_id' => $request->input('tran_id'),
                        'transaction_id' => $request->input('transaction_id'),
                        'trans_id' => $request->input('trans_id'),
                    ],
                    'request_method' => $request->method(),
                    'content_type' => $request->header('Content-Type')
                ]);
                return $this->handlePaymentError($request, 'Transaction ID is required but was not provided by SSL Commerce. Checked parameters: tran_id, transaction_id, trans_id', null);
            }

            // Find payment record with enhanced debugging
            $payment = ElsaPayment::where('tran_id', $tranId)->first();
            if (!$payment) {
                // Additional search attempts with different transaction ID formats
                $alternativePayment = ElsaPayment::where('tran_id', 'LIKE', "%{$tranId}%")->first();

                \Log::error('SSL Commerce Success: Payment record not found - ENHANCED SEARCH', [
                    'searched_tran_id' => $tranId,
                    'searched_table' => 'elsa_payments',
                    'alternative_search_result' => $alternativePayment ? 'Found similar' : 'Not found',
                    'alternative_tran_id' => $alternativePayment?->tran_id ?? 'N/A',
                    'recent_payments_count' => ElsaPayment::where('created_at', '>=', now()->subHours(24))->count(),
                    'total_payments_count' => ElsaPayment::count(),
                    'last_5_payments' => ElsaPayment::latest()->take(5)->pluck('tran_id', 'id')->toArray()
                ]);

                if ($alternativePayment) {
                    \Log::warning('SSL Commerce Success: Using alternative payment record', [
                        'original_tran_id' => $tranId,
                        'found_tran_id' => $alternativePayment->tran_id,
                        'payment_id' => $alternativePayment->id
                    ]);
                    $payment = $alternativePayment;
                    $tranId = $alternativePayment->tran_id; // Update to use the correct transaction ID
                } else {
                    return $this->handlePaymentError($request, "Payment record not found for transaction ID: {$tranId}. Please check if the payment was properly initiated.", $tranId);
                }
            }

            // Check if payment is already processed
            if ($payment->status === 'successful') {
                \Log::info('SSL Commerce Success: Payment already processed successfully', [
                    'tran_id' => $tranId,
                    'current_status' => $payment->status,
                    'payment_status' => $payment->payment_status
                ]);
                return $this->handlePaymentSuccess($request, $payment, [
                    'status' => 'VALID',
                    'amount' => $payment->grand_total,
                    'val_id' => $valId,
                    'bank_tran_id' => $payment->bank_tran_id,
                    'card_type' => $payment->card_type,
                    'message' => 'Payment already processed successfully'
                ]);
            }

            // Enhanced validation strategy determination
            $validationStrategy = $this->determineValidationStrategy($valId, $status, $amount, $bankTranId, $storeAmount, $currency);

            \Log::info('SSL Commerce Success: Using validation strategy - ENHANCED', [
                'tran_id' => $tranId,
                'strategy' => $validationStrategy,
                'payment_details' => [
                    'expected_amount' => $payment->grand_total,
                    'payment_status_in_db' => $payment->status,
                    'payment_id' => $payment->id,
                ],
                'ssl_commerce_data' => [
                    'has_val_id' => !empty($valId),
                    'has_status' => !empty($status),
                    'has_amount' => !empty($amount),
                    'has_bank_tran_id' => !empty($bankTranId),
                    'has_store_amount' => !empty($storeAmount),
                    'has_currency' => !empty($currency),
                    'status_value' => $status,
                    'amount_value' => $amount,
                    'store_amount_value' => $storeAmount,
                ],
                'validation_criteria' => [
                    'amount_tolerance' => 0.01,
                    'accepted_statuses' => ['VALID', 'VALIDATED', 'SUCCESS', 'SUCCESSFUL'],
                    'current_status_match' => !empty($status) ? in_array(strtoupper($status), ['VALID', 'VALIDATED', 'SUCCESS', 'SUCCESSFUL']) : false
                ]
            ]);

            switch ($validationStrategy) {
                case 'full_validation':
                    return $this->performFullValidation($request, $payment, $valId, $tranId, $amount);

                case 'status_based_validation':
                    return $this->performStatusBasedValidation($request, $payment, $status, $amount, $bankTranId, $cardType, $tranId);

                case 'amount_based_validation':
                    return $this->performAmountBasedValidation($request, $payment, $amount, $storeAmount, $bankTranId, $cardType, $tranId);

                case 'minimal_validation':
                    return $this->performMinimalValidation($request, $payment, $amount, $bankTranId, $cardType, $tranId);

                case 'emergency_validation':
                    return $this->performEmergencyValidation($request, $payment, $tranId, $status, $amount, $bankTranId, $cardType);

                default:
                    \Log::error('SSL Commerce Success: Unknown validation strategy', [
                        'tran_id' => $tranId,
                        'strategy' => $validationStrategy,
                        'available_params' => array_filter([
                            'val_id' => $valId,
                            'status' => $status,
                            'amount' => $amount,
                            'store_amount' => $storeAmount,
                            'bank_tran_id' => $bankTranId
                        ])
                    ]);
                    return $this->handlePaymentError($request, 'Unknown validation strategy. This should not happen - please contact support.', $tranId);
            }

        } catch (\Exception $e) {
            \Log::error('SSL Commerce Success: Exception occurred', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);
            return $this->handlePaymentError($request, 'Internal server error', $request->input('tran_id'));
        }
    }

    public function fail(Request $request)
    {
        // Handle both GET and POST requests from SSL Commerce
        $tranId = $request->input('tran_id');

        $payment = ElsaPayment::where('tran_id', $tranId)->first();

        if ($payment) {
            $payment->update([
                'status'         => 'failed',
                'payment_status' => $request->input('status') ?? 'FAILED',
            ]);
        }

        // Check if this is a redirect from SSL Commerce (browser request)
        if ($request->hasHeader('User-Agent') && !$request->wantsJson()) {
            // Redirect to frontend fail page with parameters
            $frontendUrl = env('FRONTEND_URL', 'http://localhost:5174');
            $params = http_build_query([
                'tran_id' => $tranId,
                'status' => $request->input('status') ?? 'FAILED',
            ]);
            return redirect($frontendUrl . '/payment/fail?' . $params);
        }

        return response()->json([
            'message' => 'Payment Failed',
            'transaction_id' => $tranId,
        ]);
    }

    public function cancel(Request $request)
    {
        // Handle both GET and POST requests from SSL Commerce
        $tranId = $request->input('tran_id');

        $payment = ElsaPayment::where('tran_id', $tranId)->first();

        if ($payment) {
            $payment->update([
                'status'         => 'cancelled',
                'payment_status' => $request->input('status') ?? 'CANCELLED',
            ]);
        }

        // Check if this is a redirect from SSL Commerce (browser request)
        if ($request->hasHeader('User-Agent') && !$request->wantsJson()) {
            // Redirect to frontend cancel page with parameters
            $frontendUrl = env('FRONTEND_URL', 'http://localhost:5174');
            $params = http_build_query([
                'tran_id' => $tranId,
                'status' => $request->input('status') ?? 'CANCELLED',
            ]);
            return redirect($frontendUrl . '/payment/cancel?' . $params);
        }

        return response()->json([
            'message' => 'Payment Cancelled',
            'transaction_id' => $tranId,
        ]);
    }

    public function ipn(Request $request)
    {
        try {
            // IPN is always POST, but use input() for consistency
            $tranId = $request->input('tran_id');
            $valId = $request->input('val_id');

            \Log::info('SSL Commerce IPN Received', [
                'tran_id' => $tranId,
                'val_id' => $valId,
                'all_params' => $request->all()
            ]);

            if (!$tranId || !$valId) {
                \Log::error('SSL Commerce IPN: Missing required parameters', [
                    'tran_id' => $tranId,
                    'val_id' => $valId
                ]);
                return response()->json(['message' => 'Invalid IPN data.'], 400);
            }

            $payment = ElsaPayment::where('tran_id', $tranId)->first();

            if (!$payment) {
                \Log::error('SSL Commerce IPN: Payment not found', ['tran_id' => $tranId]);
                return response()->json(['message' => 'Payment not found.'], 404);
            }

            if ($payment->status === 'successful') {
                \Log::info('SSL Commerce IPN: Payment already processed successfully', ['tran_id' => $tranId]);
                return response()->json(['message' => 'Payment already processed successfully.'], 200);
            }

            if ($payment->status === 'failed') {
                \Log::info('SSL Commerce IPN: Payment already marked as failed', ['tran_id' => $tranId]);
                return response()->json(['message' => 'Payment already marked as failed.'], 200);
            }

            // Validate with SSL Commerce
            $validationResult = $this->validateWithSSLCommerz($valId);

            if ($validationResult['success']) {
                $data = $validationResult['data'];

                // Verify transaction ID and amount
                if (isset($data['tran_id']) && $data['tran_id'] !== $tranId) {
                    \Log::error('SSL Commerce IPN: Transaction ID mismatch', [
                        'expected' => $tranId,
                        'received' => $data['tran_id']
                    ]);
                    return response()->json(['message' => 'Transaction ID mismatch.'], 422);
                }

                // Verify amount
                $expectedAmount = (float) $payment->grand_total;
                $receivedAmount = (float) ($data['amount'] ?? 0);

                if (abs($expectedAmount - $receivedAmount) > 0.01) {
                    \Log::error('SSL Commerce IPN: Amount mismatch', [
                        'expected' => $expectedAmount,
                        'received' => $receivedAmount
                    ]);
                    return response()->json(['message' => 'Amount mismatch.'], 422);
                }

                // Update payment as successful
                $payment->update([
                    'status' => 'successful',
                    'payment_status' => $data['status'],
                    'val_id' => $data['val_id'],
                    'card_type' => $data['card_type'] ?? null,
                    'store_amount' => $data['store_amount'] ?? $data['amount'] ?? null,
                    'bank_tran_id' => $data['bank_tran_id'] ?? null,
                    'transaction_date' => $data['tran_date'] ?? now(),
                ]);

                \Log::info('SSL Commerce IPN: Payment processed successfully', [
                    'tran_id' => $tranId,
                    'amount' => $payment->grand_total
                ]);

                return response()->json(['message' => 'IPN Processed Successfully'], 200);
            } else {
                \Log::error('SSL Commerce IPN: Validation failed', [
                    'tran_id' => $tranId,
                    'error' => $validationResult['error']
                ]);

                $payment->update([
                    'status' => 'failed',
                    'payment_status' => 'IPN_VALIDATION_FAILED',
                ]);

                return response()->json(['message' => 'IPN validation failed: ' . $validationResult['error']], 422);
            }

        } catch (\Exception $e) {
            \Log::error('SSL Commerce IPN: Exception occurred', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);

            return response()->json(['message' => 'Internal server error'], 500);
        }
    }

    /**
     * Determine the best validation strategy based on available parameters
     */
    private function determineValidationStrategy($valId, $status, $amount, $bankTranId, $storeAmount = null, $currency = null)
    {
        \Log::info('SSL Commerce: Determining validation strategy', [
            'val_id' => $valId,
            'status' => $status,
            'amount' => $amount,
            'bank_tran_id' => $bankTranId,
            'store_amount' => $storeAmount,
            'currency' => $currency
        ]);

        // Strategy 1: Full validation with val_id
        if (!empty($valId)) {
            \Log::info('SSL Commerce: Selected full_validation strategy (val_id available)');
            return 'full_validation';
        }

        // Strategy 2: Status-based validation (common SSL Commerce configuration)
        $validStatuses = ['VALID', 'VALIDATED', 'SUCCESS', 'SUCCESSFUL', 'COMPLETE', 'COMPLETED', 'PAID'];
        if (!empty($status) && in_array(strtoupper($status), $validStatuses)) {
            \Log::info('SSL Commerce: Selected status_based_validation strategy', [
                'status' => $status,
                'valid_statuses' => $validStatuses
            ]);
            return 'status_based_validation';
        }

        // Strategy 3: Amount-based validation (when we have amount but no clear status)
        if (!empty($amount) || !empty($storeAmount)) {
            \Log::info('SSL Commerce: Selected amount_based_validation strategy', [
                'amount' => $amount,
                'store_amount' => $storeAmount
            ]);
            return 'amount_based_validation';
        }

        // Strategy 4: Minimal validation with bank transaction ID only
        if (!empty($bankTranId)) {
            \Log::info('SSL Commerce: Selected minimal_validation strategy (bank_tran_id only)');
            return 'minimal_validation';
        }

        // Strategy 5: Emergency validation (SSL Commerce sent callback but minimal data)
        // This suggests SSL Commerce considers the payment successful
        \Log::warning('SSL Commerce: Using emergency_validation strategy (minimal data available)', [
            'available_params' => array_filter([
                'val_id' => $valId,
                'status' => $status,
                'amount' => $amount,
                'bank_tran_id' => $bankTranId,
                'store_amount' => $storeAmount,
                'currency' => $currency
            ])
        ]);
        return 'emergency_validation';
    }

    /**
     * Perform full validation using SSL Commerce API with val_id
     */
    private function performFullValidation($request, $payment, $valId, $tranId, $amount)
    {
        \Log::info('SSL Commerce Success: Performing full validation with val_id', [
            'tran_id' => $tranId,
            'val_id' => $valId
        ]);

        $validationResult = $this->validateWithSSLCommerz($valId);

        if ($validationResult['success']) {
            $validationData = $validationResult['data'];

            // Verify transaction ID matches
            if (isset($validationData['tran_id']) && $validationData['tran_id'] !== $tranId) {
                \Log::error('SSL Commerce Success: Transaction ID mismatch in full validation', [
                    'expected' => $tranId,
                    'received' => $validationData['tran_id']
                ]);
                return $this->handlePaymentError($request, "Transaction ID mismatch. Expected: {$tranId}, Received: {$validationData['tran_id']}", $tranId);
            }

            // Verify amount matches
            $expectedAmount = (float) $payment->grand_total;
            $receivedAmount = (float) ($validationData['amount'] ?? $amount ?? 0);

            if (abs($expectedAmount - $receivedAmount) > 0.01) {
                \Log::error('SSL Commerce Success: Amount mismatch in full validation', [
                    'expected' => $expectedAmount,
                    'received' => $receivedAmount,
                    'difference' => abs($expectedAmount - $receivedAmount)
                ]);
                return $this->handlePaymentError($request, "Payment amount mismatch. Expected: {$expectedAmount} BDT, Received: {$receivedAmount} BDT", $tranId);
            }

            // Payment is valid - update record
            return $this->handlePaymentSuccess($request, $payment, $validationData);
        } else {
            \Log::error('SSL Commerce Success: Full validation failed', [
                'tran_id' => $tranId,
                'val_id' => $valId,
                'error' => $validationResult['error']
            ]);
            return $this->handlePaymentError($request, 'SSL Commerce API validation failed: ' . $validationResult['error'], $tranId);
        }
    }

    /**
     * Perform status-based validation when val_id is not available
     */
    private function performStatusBasedValidation($request, $payment, $status, $amount, $bankTranId, $cardType, $tranId)
    {
        \Log::info('SSL Commerce Success: Performing status-based validation', [
            'tran_id' => $tranId,
            'status' => $status,
            'amount' => $amount,
            'bank_tran_id' => $bankTranId
        ]);

        // Verify amount if provided
        if ($amount) {
            $expectedAmount = (float) $payment->grand_total;
            $receivedAmount = (float) $amount;

            if (abs($expectedAmount - $receivedAmount) > 0.01) {
                \Log::error('SSL Commerce Success: Amount mismatch in status-based validation', [
                    'expected' => $expectedAmount,
                    'received' => $receivedAmount,
                    'difference' => abs($expectedAmount - $receivedAmount)
                ]);
                return $this->handlePaymentError($request, "Payment amount mismatch. Expected: {$expectedAmount} BDT, Received: {$receivedAmount} BDT", $tranId);
            }
        }

        // Accept as successful payment with available data
        return $this->handlePaymentSuccess($request, $payment, [
            'status' => 'VALID',
            'amount' => $amount ?? $payment->grand_total,
            'val_id' => null,
            'bank_tran_id' => $bankTranId,
            'card_type' => $cardType,
            'tran_id' => $tranId,
            'validation_method' => 'status_based'
        ]);
    }

    /**
     * Perform minimal validation with basic parameters
     */
    private function performMinimalValidation($request, $payment, $amount, $bankTranId, $cardType, $tranId)
    {
        \Log::warning('SSL Commerce Success: Performing minimal validation (limited SSL Commerce data)', [
            'tran_id' => $tranId,
            'amount' => $amount,
            'bank_tran_id' => $bankTranId,
            'note' => 'This payment will be marked as successful but requires manual verification'
        ]);

        // Verify amount if provided
        if ($amount) {
            $expectedAmount = (float) $payment->grand_total;
            $receivedAmount = (float) $amount;

            if (abs($expectedAmount - $receivedAmount) > 0.01) {
                \Log::error('SSL Commerce Success: Amount mismatch in minimal validation', [
                    'expected' => $expectedAmount,
                    'received' => $receivedAmount,
                    'difference' => abs($expectedAmount - $receivedAmount)
                ]);
                return $this->handlePaymentError($request, "Payment amount mismatch. Expected: {$expectedAmount} BDT, Received: {$receivedAmount} BDT", $tranId);
            }
        }

        // Accept payment but mark for manual verification
        return $this->handlePaymentSuccess($request, $payment, [
            'status' => 'VALID',
            'amount' => $amount ?? $payment->grand_total,
            'val_id' => null,
            'bank_tran_id' => $bankTranId,
            'card_type' => $cardType,
            'tran_id' => $tranId,
            'validation_method' => 'minimal',
            'requires_manual_verification' => true
        ]);
    }

    /**
     * Perform amount-based validation when status is unclear but amount is provided
     */
    private function performAmountBasedValidation($request, $payment, $amount, $storeAmount, $bankTranId, $cardType, $tranId)
    {
        \Log::info('SSL Commerce Success: Performing amount-based validation', [
            'tran_id' => $tranId,
            'amount' => $amount,
            'store_amount' => $storeAmount,
            'bank_tran_id' => $bankTranId,
            'note' => 'Validating based on amount match since status is unclear'
        ]);

        // Use store_amount if amount is not available
        $receivedAmount = (float) ($amount ?? $storeAmount ?? 0);
        $expectedAmount = (float) $payment->grand_total;

        if ($receivedAmount <= 0) {
            \Log::error('SSL Commerce Success: No valid amount in amount-based validation', [
                'amount' => $amount,
                'store_amount' => $storeAmount,
                'expected' => $expectedAmount
            ]);
            return $this->handlePaymentError($request, 'No valid payment amount received from SSL Commerce', $tranId);
        }

        if (abs($expectedAmount - $receivedAmount) > 0.01) {
            \Log::error('SSL Commerce Success: Amount mismatch in amount-based validation', [
                'expected' => $expectedAmount,
                'received' => $receivedAmount,
                'difference' => abs($expectedAmount - $receivedAmount)
            ]);
            return $this->handlePaymentError($request, "Payment amount mismatch. Expected: {$expectedAmount} BDT, Received: {$receivedAmount} BDT", $tranId);
        }

        // Amount matches - accept as successful payment
        return $this->handlePaymentSuccess($request, $payment, [
            'status' => 'VALID',
            'amount' => $receivedAmount,
            'val_id' => null,
            'bank_tran_id' => $bankTranId,
            'card_type' => $cardType,
            'tran_id' => $tranId,
            'validation_method' => 'amount_based'
        ]);
    }

    /**
     * Perform emergency validation when SSL Commerce sends callback with minimal data
     * This assumes SSL Commerce wouldn't send a success callback unless payment was successful
     */
    private function performEmergencyValidation($request, $payment, $tranId, $status, $amount, $bankTranId, $cardType)
    {
        \Log::warning('SSL Commerce Success: Performing emergency validation - CRITICAL', [
            'tran_id' => $tranId,
            'status' => $status,
            'amount' => $amount,
            'bank_tran_id' => $bankTranId,
            'reasoning' => 'SSL Commerce sent success callback, assuming payment is successful despite minimal data',
            'requires_immediate_manual_review' => true
        ]);

        // If we have any amount, verify it
        if ($amount) {
            $expectedAmount = (float) $payment->grand_total;
            $receivedAmount = (float) $amount;

            if (abs($expectedAmount - $receivedAmount) > 0.01) {
                \Log::error('SSL Commerce Success: Amount mismatch in emergency validation', [
                    'expected' => $expectedAmount,
                    'received' => $receivedAmount,
                    'difference' => abs($expectedAmount - $receivedAmount),
                    'action' => 'Rejecting payment due to amount mismatch even in emergency mode'
                ]);
                return $this->handlePaymentError($request, "Payment amount mismatch in emergency validation. Expected: {$expectedAmount} BDT, Received: {$receivedAmount} BDT", $tranId);
            }
        }

        // Accept payment but flag for immediate manual review
        return $this->handlePaymentSuccess($request, $payment, [
            'status' => 'VALID',
            'amount' => $amount ?? $payment->grand_total,
            'val_id' => null,
            'bank_tran_id' => $bankTranId,
            'card_type' => $cardType,
            'tran_id' => $tranId,
            'validation_method' => 'emergency',
            'requires_manual_verification' => true,
            'requires_immediate_review' => true,
            'emergency_reason' => 'SSL Commerce success callback with minimal validation data'
        ]);
    }

    /**
     * Validate payment with SSL Commerce API
     */
    private function validateWithSSLCommerz($valId)
    {
        try {
            $response = Http::timeout(30)->asForm()->get('https://securepay.sslcommerz.com/validator/api/validationserverAPI.php', [
                'val_id' => $valId,
                'store_id' => env('SSLCOMMERZ_STORE_ID'),
                'store_passwd' => env('SSLCOMMERZ_STORE_PASSWORD'),
                'v' => 1,
                'format' => 'json',
            ]);

            if (!$response->successful()) {
                return [
                    'success' => false,
                    'error' => 'SSL Commerce API request failed: ' . $response->status()
                ];
            }

            $data = $response->json();

            if (!$data) {
                return [
                    'success' => false,
                    'error' => 'Invalid response from SSL Commerce API'
                ];
            }

            if (isset($data['status']) && $data['status'] === 'VALID') {
                return [
                    'success' => true,
                    'data' => $data
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'Payment validation failed: ' . ($data['status'] ?? 'Unknown status'),
                    'data' => $data
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'SSL Commerce validation exception: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Handle successful payment
     */
    private function handlePaymentSuccess($request, $payment, $validationData)
    {
        // Prepare payment status based on validation method
        $paymentStatus = $validationData['status'] ?? 'VALID';
        if (isset($validationData['validation_method'])) {
            $paymentStatus .= '_' . strtoupper($validationData['validation_method']);
        }

        // Update payment record
        $updateData = [
            'status' => 'successful',
            'payment_status' => $paymentStatus,
            'val_id' => $validationData['val_id'] ?? null,
            'card_type' => $validationData['card_type'] ?? null,
            'store_amount' => $validationData['store_amount'] ?? $validationData['amount'] ?? null,
            'bank_tran_id' => $validationData['bank_tran_id'] ?? null,
            'transaction_date' => $validationData['tran_date'] ?? now(),
        ];

        $payment->update($updateData);

        $logData = [
            'tran_id' => $payment->tran_id,
            'amount' => $payment->grand_total,
            'validation_method' => $validationData['validation_method'] ?? 'full',
            'payment_status' => $paymentStatus,
            'updated_fields' => $updateData
        ];

        if (isset($validationData['requires_manual_verification'])) {
            $logData['manual_verification_required'] = true;
            \Log::warning('Payment marked as successful but requires manual verification', $logData);
        } else {
            \Log::info('Payment marked as successful', $logData);
        }

        // Check if this is a browser redirect from SSL Commerce
        if ($request->hasHeader('User-Agent') && !$request->wantsJson()) {
            $frontendUrl = env('FRONTEND_URL', 'http://localhost:5174');
            $params = http_build_query([
                'tran_id' => $payment->tran_id,
                'val_id' => $validationData['val_id'] ?? '',
                'amount' => $validationData['amount'] ?? $payment->grand_total,
                'status' => $validationData['status'] ?? 'VALID',
                'bank_tran_id' => $validationData['bank_tran_id'] ?? '',
                'card_type' => $validationData['card_type'] ?? ''
            ]);
            return redirect($frontendUrl . '/payment/success?' . $params);
        }

        // Return JSON response for API calls
        return response()->json([
            'message' => $validationData['message'] ?? 'Payment Successful & Validated',
            'transaction_id' => $payment->tran_id,
            'amount' => $validationData['amount'] ?? $payment->grand_total,
            'currency' => $validationData['currency'] ?? 'BDT',
            'bank_tran_id' => $validationData['bank_tran_id'] ?? null,
            'payment_status' => $validationData['status'] ?? 'VALID',
            'card_type' => $validationData['card_type'] ?? null,
            'transaction_date' => $validationData['tran_date'] ?? now()->toISOString(),
        ]);
    }

    /**
     * Handle payment error
     */
    private function handlePaymentError($request, $errorMessage, $tranId = null)
    {
        // Update payment record if transaction ID is available
        if ($tranId) {
            $payment = ElsaPayment::where('tran_id', $tranId)->first();
            if ($payment && $payment->status !== 'failed') {
                $payment->update([
                    'status' => 'failed',
                    'payment_status' => 'VALIDATION_FAILED',
                ]);
            }
        }

        // Check if this is a browser redirect from SSL Commerce
        if ($request->hasHeader('User-Agent') && !$request->wantsJson()) {
            $frontendUrl = env('FRONTEND_URL', 'http://localhost:5174');
            $params = http_build_query([
                'tran_id' => $tranId ?? '',
                'message' => $errorMessage,
                'error' => 'validation_failed'
            ]);
            return redirect($frontendUrl . '/payment/fail?' . $params);
        }

        // Return JSON response for API calls
        return response()->json([
            'message' => $errorMessage,
            'transaction_id' => $tranId,
            'error' => 'validation_failed'
        ], 422);
    }

    /**
     * Debug endpoint to help troubleshoot SSL Commerce integration
     */
    public function debug(Request $request)
    {
        $debugInfo = [
            'timestamp' => now()->toISOString(),
            'request_method' => $request->method(),
            'request_url' => $request->fullUrl(),
            'ip_address' => $request->ip(),
            'user_agent' => $request->header('User-Agent'),
            'content_type' => $request->header('Content-Type'),
            'all_headers' => $request->headers->all(),
            'query_parameters' => $request->query(),
            'post_parameters' => $request->request->all(),
            'all_input' => $request->all(),
            'extracted_ssl_params' => [
                'tran_id' => $request->input('tran_id') ?? $request->input('transaction_id') ?? $request->input('trans_id'),
                'val_id' => $request->input('val_id') ?? $request->input('validation_id'),
                'amount' => $request->input('amount') ?? $request->input('store_amount') ?? $request->input('total_amount'),
                'status' => $request->input('status') ?? $request->input('payment_status') ?? $request->input('transaction_status'),
                'bank_tran_id' => $request->input('bank_tran_id') ?? $request->input('bank_transaction_id'),
                'card_type' => $request->input('card_type') ?? $request->input('card_brand'),
                'currency' => $request->input('currency'),
                'store_amount' => $request->input('store_amount'),
            ],
            'ssl_commerce_detection' => [
                'likely_ssl_commerce' => $this->isLikelySSLCommerceRequest($request),
                'has_ssl_params' => $this->hasSSLCommerceParams($request),
                'validation_strategy' => $this->determineValidationStrategy(
                    $request->input('val_id'),
                    $request->input('status'),
                    $request->input('amount'),
                    $request->input('bank_tran_id'),
                    $request->input('store_amount'),
                    $request->input('currency')
                )
            ]
        ];

        // If transaction ID is provided, get payment details
        $tranId = $request->input('tran_id') ?? $request->input('transaction_id') ?? $request->input('trans_id');
        if ($tranId) {
            $payment = ElsaPayment::where('tran_id', $tranId)->first();
            $debugInfo['payment_record'] = $payment ? [
                'id' => $payment->id,
                'tran_id' => $payment->tran_id,
                'status' => $payment->status,
                'payment_status' => $payment->payment_status,
                'grand_total' => $payment->grand_total,
                'created_at' => $payment->created_at,
                'updated_at' => $payment->updated_at,
            ] : 'Payment record not found';
        }

        \Log::info('SSL Commerce Debug Endpoint Called', $debugInfo);

        return response()->json([
            'message' => 'SSL Commerce Debug Information',
            'debug_info' => $debugInfo,
            'recommendations' => $this->getDebugRecommendations($debugInfo)
        ]);
    }

    /**
     * Get payment status for a specific transaction ID
     */
    public function getPaymentStatus($tranId)
    {
        $payment = ElsaPayment::where('tran_id', $tranId)->first();

        if (!$payment) {
            return response()->json([
                'message' => 'Payment not found',
                'transaction_id' => $tranId
            ], 404);
        }

        return response()->json([
            'message' => 'Payment status retrieved',
            'payment' => [
                'id' => $payment->id,
                'tran_id' => $payment->tran_id,
                'status' => $payment->status,
                'payment_status' => $payment->payment_status,
                'grand_total' => $payment->grand_total,
                'val_id' => $payment->val_id,
                'bank_tran_id' => $payment->bank_tran_id,
                'card_type' => $payment->card_type,
                'store_amount' => $payment->store_amount,
                'transaction_date' => $payment->transaction_date,
                'created_at' => $payment->created_at,
                'updated_at' => $payment->updated_at,
            ]
        ]);
    }

    /**
     * Check if request is likely from SSL Commerce
     */
    private function isLikelySSLCommerceRequest($request)
    {
        $sslIndicators = [
            'tran_id', 'val_id', 'bank_tran_id', 'card_type', 'store_amount',
            'APIConnect', 'verified_on', 'processing_type'
        ];

        $foundIndicators = 0;
        foreach ($sslIndicators as $indicator) {
            if ($request->has($indicator)) {
                $foundIndicators++;
            }
        }

        return $foundIndicators >= 2;
    }

    /**
     * Check if request has SSL Commerce parameters
     */
    private function hasSSLCommerceParams($request)
    {
        return $request->has('tran_id') ||
               $request->has('transaction_id') ||
               $request->has('val_id') ||
               $request->has('bank_tran_id');
    }

    /**
     * Get debug recommendations based on the request
     */
    private function getDebugRecommendations($debugInfo)
    {
        $recommendations = [];

        if (!$debugInfo['ssl_commerce_detection']['likely_ssl_commerce']) {
            $recommendations[] = 'This does not appear to be a valid SSL Commerce callback';
        }

        if (!isset($debugInfo['extracted_ssl_params']['tran_id']) || empty($debugInfo['extracted_ssl_params']['tran_id'])) {
            $recommendations[] = 'Missing transaction ID - check SSL Commerce configuration';
        }

        if ($debugInfo['ssl_commerce_detection']['validation_strategy'] === 'emergency_validation') {
            $recommendations[] = 'Very limited data available - contact SSL Commerce support to ensure all parameters are being sent';
        }

        if (isset($debugInfo['payment_record']) && $debugInfo['payment_record'] === 'Payment record not found') {
            $recommendations[] = 'Payment record not found in database - ensure payment was properly initiated';
        }

        return $recommendations;
    }
}
