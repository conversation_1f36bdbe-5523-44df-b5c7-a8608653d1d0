<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // 'email_or_username' => [
            //     'required',
            //     function ($attribute, $value, $fail) {
            //         $isEmail = filter_var($value, FILTER_VALIDATE_EMAIL);
            //         $column = $isEmail ? 'email' : 'username';

            //         if (! \App\Models\User::where($column, $value)->exists()) {
            //             $fail("The provided $column does not exist.");
            //         }
            //     },
            // ],
            'email_or_username' => 'required',
            'user_type' => 'required',
        ];
    }
}
