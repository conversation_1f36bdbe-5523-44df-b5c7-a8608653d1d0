<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateElsaPackageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

    public function authorize(): bool
    {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'duration' => 'required|string|max:255',
            'before_price' => 'required|numeric|min:0|max:999999.99',
            'price' => 'required|numeric|min:0|max:999999.99',
            'popular' => 'required|boolean',
            'description' => 'required|string',
            'months' => 'required|integer|min:1',
            'billing_type' => 'required|in:monthly,yearly,daily',
            'tag' => 'nullable|string|max:255',
            'category_id' => 'nullable|integer|exists:elsa_categories,id',
            'button_text' => 'nullable|string|max:255',
            'button_type' => 'nullable|string|max:255',
            'is_active' => 'required|boolean',
            'features' => 'nullable|array',
        ];
    }
}
