<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateUserInformationRequest extends FormRequest
{
    public function authorize(): bool
    {
        // Only allow if the user is authenticated
        return auth()->check();
    }

    public function rules(): array
    {
        return [
            'name'             => 'required|string|max:255',
            'profile_image'    => 'nullable|file|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'primary_number'   => 'nullable|string|max:50',
            'alternate_number' => 'nullable|string|max:50',
            'email'            => 'nullable|string|email|max:255',
            'date_of_birth'    => 'nullable|string',
            'religion'         => 'nullable|string|max:255',
            'fathers_name'     => 'nullable|string|max:255',
            'mothers_name'     => 'nullable|string|max:255',
            'father_number'    => 'nullable|string|max:50',
            'mothar_number'    => 'nullable|string|max:50',
            'gender'           => 'required|in:Male,Female,Other',
            'blood_group'      => 'nullable|string|max:10',
            'bio'              => 'nullable|string|max:500',
            'department'       => 'nullable|string|max:255',
            'subject'          => 'nullable|string|max:255',
            'present_division_id'=> 'nullable|integer',
            'present_district_id'=> 'nullable|integer',
            'present_area_id'=> 'nullable|integer',
            'present_address'=> 'nullable|string|max:255',
            'permanent_division_id'=> 'nullable|integer',
            'permanent_district_id'=> 'nullable|integer',
            'present_upazila_id'=> 'nullable|integer',
            'permanent_upazila_id'=> 'nullable|integer',
            'permanent_area_id'=> 'nullable|integer',
            'permanent_address'=> 'nullable|string|max:255',
            'nid_no'=> 'nullable|string|max:255',
            'birth_certificate_no'=> 'nullable|string|max:255',
            'passport_no'=> 'nullable|string|max:255',
        ];
    }
}
