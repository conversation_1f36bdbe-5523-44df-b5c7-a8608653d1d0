<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StorePaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'            => 'required|string|max:255',
            'email'           => 'required|email',
            'phone'           => 'required|string|max:20',
            'organization'    => 'nullable|string|max:255',
            'address'         => 'required|string|max:500',
            'no_of_user'      => 'required|integer|min:1',
            'elsa_package_id' => 'required|integer|exists:elsa_packages,id',
            'month'           => 'required|integer|min:1',
            'unit_price'      => 'required|numeric|min:0',
            'discount'        => 'nullable|numeric|min:0',
        ];
    }
}
