<?php

namespace DummyNamespace;

use App\Models\DummyModel;
use App\Http\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class DummyClass
{
    use HelperTrait;

    public function index(Request $request): Collection|LengthAwarePaginator|array
    {
        $query = DummyModel::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareDummyModelData($request);

        return DummyModel::create($data);
    }

    private function prepareDummyModelData(Request $request, bool $isNew = true): array
    {
        // Get the fillable fields from the model
        $fillable = (new DummyModel())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = $request->only($fillable);

        // Handle file uploads
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'dummyModel');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'dummyModel');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            $data['created_by'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): DummyModel
    {
        return DummyModel::findOrFail($id);
    }

    public function update(Request $request, int $id)
    {
        $dummyModel = DummyModel::findOrFail($id);
        $updateData = $this->prepareDummyModelData($request, false);
        $dummyModel->update($updateData);

        return $dummyModel;
    }

    public function destroy(int $id): bool
    {
        $dummyModel = DummyModel::findOrFail($id);
        $dummyModel->name .= '_' . Str::random(8);
        $dummyModel->deleted_at = now();

        return $dummyModel->save();
    }
}
