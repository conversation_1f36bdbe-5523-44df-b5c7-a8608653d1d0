<?php

namespace App\Services;

use App\Http\Traits\HelperTrait;
use App\Models\AppliedJob;
use App\Models\TutorJob;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardService
{
    use HelperTrait;

    /**
     * Get admin dashboard statistics
     *
     * @param Request $request
     * @return array
     */
    public function adminDashboard(Request $request)
    {
        $data = [];

        // Count statistics
        $data['counts'] = [
            'total_jobs' => TutorJob::count(),
            'open_jobs' => TutorJob::where('job_status', 'Open')->count(),
            'linked_jobs' => AppliedJob::where('is_linked_up', true)->count(),
            'total_tutors' => User::where('user_type', 'Teacher')->count(),
            'total_students' => User::where('user_type', 'Student')->count(),
            'total_guardians' => User::where('user_type', 'Guardian')->count(),
        ];

        // Top 5 tutors (based on number of linked jobs)
        $data['top_tutors'] = User::where('users.user_type', 'Teacher')
            ->select([
                'users.id',
                'users.name',
                'users.email',
                'users.username',
                'users.profile_image',
                'users.tutor_code',
                DB::raw('COUNT(applied_jobs.id) as linked_jobs_count')
            ])
            ->leftJoin('applied_jobs', function ($join) {
                $join->on('users.id', '=', 'applied_jobs.tutor_id')
                    ->where('applied_jobs.is_linked_up', true);
            })
            ->groupBy('users.id')
            ->orderBy('linked_jobs_count', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($tutor) {
                // Add institute from education history if needed
                $tutor->institute = DB::table('tutor_education_histories')
                    ->where('user_id', $tutor->id)
                    ->orderBy('passing_year', 'desc')
                    ->orderBy('sequence', 'desc')
                    ->value('institute');

                // Add review rating (currently random as in other parts of the app)
                $tutor->review = 4.3;
                return $tutor;
            });

        // Top 5 students (based on number of posted jobs)
        $data['top_students'] = User::where('users.user_type', 'Student')
            ->select([
                'users.id',
                'users.name',
                'users.email',
                'users.username',
                'users.profile_image',
                DB::raw('COUNT(tutor_jobs.id) as posted_jobs_count')
            ])
            ->leftJoin('tutor_jobs', 'users.id', '=', 'tutor_jobs.user_id')
            ->groupBy('users.id')
            ->orderBy('posted_jobs_count', 'desc')
            ->limit(5)
            ->get();

        // Top 5 jobs (based on number of applications)
        $data['top_jobs'] = TutorJob::select([
            'tutor_jobs.id',
            'tutor_jobs.job_title',
            'tutor_jobs.salary_amount',
            'tutor_jobs.job_id',
            'tutor_jobs.created_at',
            DB::raw('COUNT(applied_jobs.id) as applications_count')
        ])
            ->leftJoin('applied_jobs', 'tutor_jobs.id', '=', 'applied_jobs.job_id')
            ->with(['medium:id,name_en', 'subjects:id,name_en', 'grade:id,name_en'])
            ->groupBy('tutor_jobs.id')
            ->orderBy('applications_count', 'desc')
            ->limit(5)
            ->get();

        // Job posts by month for the current year (for bar chart)
        $currentYear = Carbon::now()->year;


        // Daily job statistics for the current month or specified month
        $targetMonth = $request->month ? (int)$request->month : Carbon::now()->month;
        $targetYear = $request->year ? (int)$request->year : $currentYear;

        // Get the number of days in the target month
        $daysInMonth = Carbon::createFromDate($targetYear, $targetMonth, 1)->daysInMonth;

        // Query daily job counts for the target month
        $dailyJobStats = TutorJob::select(
            DB::raw('DAY(created_at) as day'),
            DB::raw('COUNT(*) as job_count')
        )
            ->whereYear('created_at', $targetYear)
            ->whereMonth('created_at', $targetMonth)
            ->groupBy(DB::raw('DAY(created_at)'))
            ->orderBy('day')
            ->get()
            ->keyBy('day')
            ->map(fn($item) => $item->job_count)
            ->toArray();

        // Fill in missing days with zero counts and format as required
        $data['daily_job_stats'] = [];
        for ($day = 1; $day <= $daysInMonth; $day++) {
            $date = Carbon::createFromDate($targetYear, $targetMonth, $day)->format('Y-m-d');
            $data['daily_job_stats'][] = [
                'date' => $date,
                'total_jobs' => $dailyJobStats[$day] ?? 0
            ];
        }

        return $data;
    }

    /**
     * Get daily job statistics for a specific month
     *
     * @param Request $request
     * @return array
     */
    public function getDailyJobStats(Request $request)
    {
        // Get target month and year from request or use current month/year
        $targetMonth = $request->month ? (int)$request->month : Carbon::now()->month;
        $targetYear = $request->year ? (int)$request->year : Carbon::now()->year;

        // Get the number of days in the target month
        $daysInMonth = Carbon::createFromDate($targetYear, $targetMonth, 1)->daysInMonth;

        // Query daily job counts for the target month
        $dailyJobStats = TutorJob::select(
            DB::raw('DAY(created_at) as day'),
            DB::raw('COUNT(*) as job_count')
        )
            ->whereYear('created_at', $targetYear)
            ->whereMonth('created_at', $targetMonth)
            ->groupBy(DB::raw('DAY(created_at)'))
            ->orderBy('day')
            ->get()
            ->keyBy('day')
            ->map(fn($item) => $item->job_count)
            ->toArray();

        // Fill in missing days with zero counts and format as required
        $result = [];
        for ($day = 1; $day <= $daysInMonth; $day++) {
            $date = Carbon::createFromDate($targetYear, $targetMonth, $day)->format('Y-m-d');
            $result[] = [
                'date' => $date,
                'total_jobs' => $dailyJobStats[$day] ?? 0
            ];
        }

        return [
            'daily_job_stats' => $result,
            'month' => Carbon::createFromDate($targetYear, $targetMonth, 1)->format('F'),
            'year' => $targetYear
        ];
    }
}
