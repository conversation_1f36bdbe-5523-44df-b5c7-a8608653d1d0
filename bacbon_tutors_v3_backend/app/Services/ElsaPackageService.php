<?php

namespace App\Services;

use App\Models\ElsaPackage;
use App\Http\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ElsaPackageService
{
    use HelperTrait;

    public function index(Request $request): Collection|LengthAwarePaginator|array
    {
        $query = ElsaPackage::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareElsaPackageData($request);

        return ElsaPackage::create($data);
    }

    private function prepareElsaPackageData(Request $request, bool $isNew = true): array
    {
        // Get the fillable fields from the model
        $fillable = (new ElsaPackage())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = $request->only($fillable);

        // Handle file uploads
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'elsaPackage');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'elsaPackage');

        // Add created_by and created_at fields for new records
        if ($isNew) {

            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): ElsaPackage
    {
        return ElsaPackage::findOrFail($id);
    }

    public function update(Request $request, int $id)
    {
        $elsaPackage = ElsaPackage::findOrFail($id);
        $updateData = $this->prepareElsaPackageData($request, false);
        $elsaPackage->update($updateData);

        return $elsaPackage;
    }

    public function destroy(int $id): bool
    {
        $elsaPackage = ElsaPackage::findOrFail($id);
        $elsaPackage->name .= '_' . Str::random(8);
        $elsaPackage->deleted_at = now();

        return $elsaPackage->save();
    }
    public function elsaPackages($category_id)
    {
        $elsaPackages = ElsaPackage::where('category_id', $category_id)->where('is_active', 1)->get();

        return $elsaPackages;
    }
}
