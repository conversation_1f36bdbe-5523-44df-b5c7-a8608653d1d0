<?php

namespace App\Services;

use App\Models\ElsaCategory;
use App\Http\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ElsaCategoryService
{
    use HelperTrait;

    public function index(Request $request): Collection|LengthAwarePaginator|array
    {
        $query = ElsaCategory::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['title']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareElsaCategoryData($request);

        return ElsaCategory::create($data);
    }

    private function prepareElsaCategoryData(Request $request, bool $isNew = true): array
    {
        // Get the fillable fields from the model
        $fillable = (new ElsaCategory())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = $request->only($fillable);

        // Handle file uploads
        $data['image'] = $this->fileUpload($request, 'image', 'elsaCategory');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'elsaCategory');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            // $data['created_by'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): ElsaCategory
    {
        return ElsaCategory::findOrFail($id);
    }

    public function update(Request $request, int $id)
    {
        $elsaCategory = ElsaCategory::findOrFail($id);
        $updateData = $this->prepareElsaCategoryData($request, false);
        $elsaCategory->update($updateData);

        return $elsaCategory;
    }

    public function destroy(int $id): bool
    {
        $elsaCategory = ElsaCategory::findOrFail($id);
        $elsaCategory->delete();

        return $elsaCategory->save();
    }

    public function elsaCategories($request)
    {
        return ElsaCategory::with('packages')->
        where('is_active', 1)->get();
    }
}
