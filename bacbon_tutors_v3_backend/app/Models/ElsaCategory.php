<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use SoftDeletes;

class ElsaCategory extends Model
{
    protected $fillable = [
        'title',
        'subtitle',
        'description',
        'image',
        'badge',
        'is_active'
    ];



    protected $casts = [
        'is_active' => 'boolean'
    ];

    public function packages()
    {
        return $this->hasMany(ElsaPackage::class, 'category_id', 'id');
    }
}
