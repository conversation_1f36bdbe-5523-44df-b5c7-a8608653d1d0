<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HomeCarousel extends Model
{
    use HasFactory;

    protected $table = 'home_carousels';

    protected $fillable = [
        'title',
        'description',
        'thumbnail',
        'background_color',
        'has_button',
        'button_text',
        'button_link',
        'sorting_order',
        'is_active',
        'url',
        'has_tutor',
        'after_login_button_text'
    ];

    protected $casts = [
        'has_button' => 'boolean',
        'is_active' => 'boolean',
        'has_tutor'=> 'boolean'
    ];
}
