<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use SoftDeletes;

class ElsaPackage extends Model
{
    protected $fillable = [
        'duration',
        'before_price',
        'price',
        'popular',
        'description',
        'months',
        'billing_type',
        'tag',
        'category_id',
        'button_text',
        'button_type',
        'is_active',
        'features',
    ];

    protected $casts = [
        'features' => 'array',
    ];
}
