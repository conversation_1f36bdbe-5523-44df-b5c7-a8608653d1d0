<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ElsaPayment extends Model
{
    protected $fillable = [
        'name',
        'email',
        'phone',
        'organization',
        'address',
        'no_of_user',
        'elsa_package_id',
        'month',
        'total_amount',
        'unit_price',
        'discount',
        'grand_total',
        'user_id',
        'card_type',
        'currency',
        'tran_id',
        'payment_status',
        'is_promo_applied',
        'promo_id',
        'status',
    ];
    
}
